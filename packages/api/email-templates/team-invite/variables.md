# Team Invite Template Variables

This document describes the variables available in the team invite email template.

## Required Variables

- `{{recipientName}}` - Name of the person being invited (e.g., "<PERSON>")
- `{{teamName}}` - Name of the team they're being invited to (e.g., "Acme Real Estate")
- `{{inviterName}}` - Name of the person sending the invitation (e.g., "<PERSON>")
- `{{inviteUrl}}` - URL to accept the team invitation (e.g., "https://app.imoblr.com/aceitar-convite?token=tokenId&email=<EMAIL>")
- `{{expiryDate}}` - When the invitation expires (e.g., "January 15, 2024")

## Optional Variables

- `{{supportEmail}}` - Support contact email (default: "<EMAIL>")
- `{{unsubscribeUrl}}` - URL to unsubscribe from team invitations

## Usage Example

```typescript
const variables = {
  recipientName: "<PERSON>",
  teamName: "Acme Real Estate",
  inviterName: "<PERSON>", 
  inviteUrl: "https://app.imoblr.com/aceitar-convite?token=tokenId&email=<EMAIL>",
  expiryDate: "January 15, 2024",
  supportEmail: "<EMAIL>",
  unsubscribeUrl: "https://app.imoblr.com/unsubscribe/team-invites"
};
```

## Notes

- All variables should be properly escaped to prevent XSS attacks
- URLs should be validated before being inserted into templates
- Dates should be formatted in a user-friendly format
- Names should be properly capitalized
