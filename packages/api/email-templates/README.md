# Email Templates

This directory contains all transactional email templates for the Imoblr platform.

## Structure

Each email template should be organized in its own subdirectory with the following files:

```
email-templates/
├── template-name/
│   ├── template.ts        # TypeScript template definition (used by service)
│   ├── template.html      # HTML version (for reference/editing)
│   ├── template.txt       # Plain text version (for reference/editing)
│   ├── subject.txt        # Email subject line (for reference)
│   └── variables.md       # Documentation of available variables
├── index.ts               # Template registry
└── README.md
```

**Note:** The email service loads templates from the TypeScript registry (`index.ts`) for optimal performance in the Cloudflare Worker environment. The individual `.html`, `.txt`, and `.subject.txt` files are maintained for easy editing and reference.

## Template Variables

Templates use the `{{variable}}` syntax for variable substitution. Common variables include:

- `{{recipientName}}` - Name of the email recipient
- `{{senderName}}` - Name of the person/system sending the email
- `{{companyName}}` - Company/platform name
- `{{actionUrl}}` - URL for primary call-to-action
- `{{supportEmail}}` - Support contact email

## Available Templates

- **team-invite** - Invitation to join a team
- More templates will be added as needed

## Usage

Templates are loaded from the TypeScript registry and processed by the EmailService class in `services/email.ts`. The service handles variable substitution and email sending via Resend.

Example:
```typescript
const emailService = createEmailService(env);
const template = await emailService.loadTemplate('team-invite', {
  recipientName: 'John Doe',
  teamName: 'Acme Corp',
  inviterName: 'Jane Smith',
  inviteUrl: 'https://app.imoblr.com/aceitar-convite?token=tokenId&email=<EMAIL>',
  expiryDate: 'January 15, 2024',
  supportEmail: '<EMAIL>',
  unsubscribeUrl: 'https://app.imoblr.com/unsubscribe/team-invites'
});

await emailService.sendEmail(template, {
  to: '<EMAIL>',
  idempotencyKey: 'team-invite-unique-id'
});
```

## Adding New Templates

1. Create a new directory under `email-templates/` with your template name
2. Add the template definition to `template.ts` in that directory
3. Export the template in `email-templates/index.ts`
4. Create reference files (`.html`, `.txt`, `.subject.txt`) for easy editing
5. Document variables in `variables.md`
