import type { EmailTemplate } from '../services/email';
import { teamInviteTemplate } from './team-invite/template';

/**
 * Template registry for all email templates
 * This allows us to import templates as modules in the Cloudflare Worker environment
 */
export const emailTemplates: Record<string, EmailTemplate> = {
  'team-invite': teamInviteTemplate,
  // Add more templates here as needed
  // 'welcome': welcomeTemplate,
  // 'password-reset': passwordResetTemplate,
  // etc.
};

/**
 * Get a template by name
 */
export function getTemplate(templateName: string): EmailTemplate | null {
  return emailTemplates[templateName] || null;
}

/**
 * Get all available template names
 */
export function getAvailableTemplates(): string[] {
  return Object.keys(emailTemplates);
}
