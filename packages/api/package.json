{"name": "@platform/api", "version": "1.0.0", "description": "Consolidated API worker for Imoblr", "main": "index.ts", "scripts": {"build": "nx build api", "dev": "nx serve api", "deploy": "nx deploy api", "drizzle:generate": "drizzle-kit generate:pg", "drizzle:push": "drizzle-kit push:pg"}, "author": "", "license": "ISC", "devDependencies": {"@cloudflare/workers-types": "^4.20240320.0", "drizzle-kit": "^0.21.1", "typescript": "^5.0.0", "wrangler": "^4.6.0"}, "dependencies": {"@cf-wasm/photon": "^0.1.29", "@supabase/supabase-js": "^2.43.4", "aws4fetch": "^1.0.20", "drizzle-orm": "^0.30.5", "hono": "^4.7.5", "jose": "^5.3.0", "postgres": "^3.4.3", "resend": "^4.7.0", "stripe": "^18.0.0"}}