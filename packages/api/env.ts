import type { Context, Next } from "hono";

export const envVars = [
  "SUPABASE_URL",
  "SUPABASE_SERVICE_ROLE_KEY",
  "SUPABASE_JWT_SECRET",
  "SUPABASE_ANON_KEY", // Added anon key for user-context operations
  "GOOGLE_API_KEY",
  "STRIPE_API_KEY",
  "STRIPE_WEBHOOK_SIGNING_SECRET", // Added for webhook signature verification
  "DEFAULT_TRIAL_PLAN_LOOKUP_KEY", // Default trial plan lookup key
  "DATABASE_URL", // Direct database connection URL for Drizzle ORM
  "RESEND_API_KEY", // Resend API key for sending transactional emails
  "FRONTEND_URL" // Frontend application URL for generating links
] as const;

export const productionOnlyEnvVars = [
  "CLOUDFLARE_ACCOUNT_ID",
  "CLOUDFLARE_CUSTOM_HOSTNAMES_API_TOKEN",
  "CLOUDFLARE_ZONE_ID",
  "R2_BUCKET_URL",
  "R2_PUBLIC_URL",
  "R2_ACCESS_KEY_ID",
  "R2_SECRET_ACCESS_KEY",
] as const;

export type Env = {
  // Define the type to include all possible variables
  [K in (typeof envVars)[number]]: string; // Base vars are always required strings
} & {
  [K in (typeof productionOnlyEnvVars)[number]]: string; // Prod vars are required in the type definition
} & {
  CF_RAY?: string; // Cloudflare Workers runtime variable for environment detection
} & {};

export const checkRequiredEnvVars = (env: Env): Response | null => {
  // Always check the base envVars
  const missingBaseVars = envVars.filter((varName) => !env[varName]);

  let missingProdVars: string[] = [];

  // Check if we're in development mode
  // In Cloudflare Workers, wrangler dev doesn't set ENVIRONMENT, but we can detect it
  // by checking if we're running locally (no CF-Ray header in dev mode)
  const isDevelopment = 
    process.env.ENVIRONMENT === "development" ||
    process.env.NODE_ENV === "development" ||
    !env.CF_RAY; // CF_RAY is only present in production Cloudflare Workers

  if (!isDevelopment) {
    missingProdVars = productionOnlyEnvVars.filter((varName) => !env[varName]);
  }

  const allMissingVars = [...missingBaseVars, ...missingProdVars];

  if (allMissingVars.length > 0) {
    const errorMessage = `Missing environment variables: ${allMissingVars.join(", ")}`;
    console.error(errorMessage);
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }

  return null;
};

// Hono middleware that checks for required environment variables
// Ensure the Context binding uses the updated Env type
export const checkRequiredEnvVarsMiddleware = async (c: Context<{ Bindings: Env }>, next: Next) => {
  // Pass the environment bindings (c.env) which should conform to the Env type
  const response = checkRequiredEnvVars(c.env);

  if (response) {
    return response;
  }

  return await next();
};
