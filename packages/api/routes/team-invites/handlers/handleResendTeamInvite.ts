import type { Env } from "../../../types";
import { createDbClient } from "../../../utils/db";
import { createUnauthorizedResponse } from "../../../utils/auth";
import { isTeamOwner } from "../../../utils/teamRoleCheck";
import { teamInvites, userAccounts } from "../../../schema/billing";
import { eq, and, sql } from "drizzle-orm";
import type { Context } from "hono";
import { createEmailService } from "../../../services/email";

const generateInviteToken = () => {
    return crypto.randomUUID() + crypto.randomUUID();
  };


type HandlerVariables = { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };

// POST /team-invites/resend/:inviteId
export const handleResendTeamInvite = async (c: Context<{ Bindings: Env; Variables: HandlerVariables }>) => {
  try {
    const auth = c.get("auth");
    const inviteId = c.req.param('inviteId');

    if (!auth?.isAuthenticated || !auth.userId) {
      return createUnauthorizedResponse("Authentication required");
    }

    if (!inviteId) {
      return c.json({ error: "Missing inviteId" }, 400);
    }

    const db = createDbClient(c.env);

    const inviteResult = await db
      .select({
        teamId: teamInvites.teamId,
        email: teamInvites.email,
        teamName: teamInvites.teamName,
      })
      .from(teamInvites)
      .where(eq(teamInvites.id, inviteId))
      .limit(1);

    if (inviteResult.length === 0) {
      return c.json({ error: "Invitation not found" }, 404);
    }

    const invite = inviteResult[0];

    const isOwner = await isTeamOwner(auth.userId, invite.teamId, c.env);
    if (!isOwner) {
      return c.json({ error: "Only team owners can resend invitations" }, 403);
    }

    const token = generateInviteToken();
    const now = new Date();
    const expiresAt = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days from now

    await db
      .update(teamInvites)
      .set({
        token: token,
        expiresAt: expiresAt,
        updatedAt: now,
      })
      .where(eq(teamInvites.id, inviteId));

    // Send invitation email
    try {
      const inviterResult = await db
        .select({
          firstName: userAccounts.firstName,
          lastName: userAccounts.lastName,
          email: userAccounts.email,
        })
        .from(userAccounts)
        .where(eq(userAccounts.userId, auth.userId))
        .limit(1);

      const inviter = inviterResult.length > 0 ? inviterResult[0] : null;
      const inviterName = inviter
        ? `${inviter.firstName || ''} ${inviter.lastName || ''}`.trim() || inviter.email || 'Someone'
        : 'Someone';

      const emailService = createEmailService(c.env);
      const baseUrl = c.env.FRONTEND_URL || 'https://app.imoblr.com';
      const inviteUrl = `${baseUrl}/aceitar-convite?token=${token}&email=${invite.email}`;

      const expiryDate = new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'UTC'
      }).format(expiresAt);

      const template = await emailService.loadTemplate('team-invite', {
        recipientName: invite.email.split('@')[0],
        recipientEmail: invite.email,
        teamName: invite.teamName || 'Unknown Team',
        inviterName: inviterName,
        inviteUrl: inviteUrl,
        expiryDate: expiryDate,
        supportEmail: '<EMAIL>',
        unsubscribeUrl: `${baseUrl}/unsubscribe/team-invites`
      });

      const emailResult = await emailService.sendEmail(template, {
        to: invite.email,
        idempotencyKey: `team-invite-resend-${inviteId}`
      });

      if (!emailResult.success) {
        console.error(`Failed to resend invitation email to ${invite.email}:`, emailResult.error);
      } else {
        console.log(`Successfully resent invitation email to ${invite.email} with message ID: ${emailResult.messageId}`);
      }
    } catch (emailError) {
      console.error(`Error resending invitation email to ${invite.email}:`, emailError);
    }

    return c.json({ success: true });

  } catch (error) {
    console.error("Error resending team invitation:", error);
    return c.json({
      error: error instanceof Error ? error.message : "Failed to resend team invitation"
    }, 500);
  }
};