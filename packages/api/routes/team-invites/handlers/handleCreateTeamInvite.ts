import type { Env } from "../../../types";
import { createDbClient } from "../../../utils/db";
import { createUnauthorizedResponse } from "../../../utils/auth";
import { isTeamOwner } from "../../../utils/teamRoleCheck";
import { teamAccounts, teamInvites, userAccounts } from "../../../schema/billing";
import { eq, and, sql } from "drizzle-orm";
import type { Context } from "hono";
import { createEmailService } from "../../../services/email";

interface CreateTeamInviteRequest {
  team_id: string;
  email: string;
  team_role?: 'owner' | 'member';
}

type HandlerVariables = { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };

// POST /team-invites
export const handleCreateTeamInvite = async (c: Context<{ Bindings: Env; Variables: HandlerVariables }>) => {
  try {
    // Get auth from context (set by middleware)
    const auth = c.get("auth");

    if (!auth?.isAuthenticated || !auth.userId) {
      return createUnauthorizedResponse("Authentication required");
    }

    const body = await c.req.json() as CreateTeamInviteRequest;
    const { team_id, email, team_role = 'member' } = body;

    // Validate required fields
    if (!team_id || !email) {
      return c.json({ error: "Missing required fields: team_id, email" }, 400);
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return c.json({ error: "Invalid email format" }, 400);
    }

    // Validate team_role
    if (!['owner', 'member'].includes(team_role)) {
      return c.json({ error: "Invalid team_role. Must be 'owner' or 'member'" }, 400);
    }

    // Check if user is owner of the team
    const isOwner = await isTeamOwner(auth.userId, team_id, c.env);

    if (!isOwner) {
      return c.json({ error: "Only team owners can create invitations" }, 403);
    }

    // Use Drizzle ORM with direct database connection (same as billing endpoint)
    const db = createDbClient(c.env);

    // Check if team exists and get team name
    const teamResult = await db
      .select({
        name: teamAccounts.name,
      })
      .from(teamAccounts)
      .where(eq(teamAccounts.id, team_id))
      .limit(1);

    if (teamResult.length === 0) {
      return c.json({ error: "Team not found" }, 404);
    }

    const team = teamResult[0];

    // Check if the email being invited is already associated with a user who is a member of the team
    // We need to join with auth.users to check if the email is already associated with a user
    // For now, we'll skip this check and rely on the invitation duplicate check below
    // TODO: Add a proper check to see if the email is already associated with a team member

    // Check if there's already a pending invitation for this email and team
    const existingInviteResult = await db
      .select({
        id: teamInvites.id,
      })
      .from(teamInvites)
      .where(and(
        eq(teamInvites.teamId, team_id),
        eq(teamInvites.email, email)
      ))
      .limit(1);

    // If there's a result, it means there's already a pending invitation
    if (existingInviteResult.length > 0) {
      return c.json({ error: "An invitation for this email already exists for this team" }, 400);
    }

    // Generate a unique token for the invitation
    const token = generateInviteToken();

    // Create the team invitation using raw SQL to bypass Drizzle field mapping issue
    const inviteId = crypto.randomUUID();
    const now = new Date();
    const expiresAt = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days from now

    const newInviteResult = await db.execute(sql`
      INSERT INTO basejump.team_invites (
        id, team_role, team_id, token, invited_by_user_id, team_name, email, updated_at, created_at, expires_at
      ) VALUES (
        ${inviteId}, ${team_role}, ${team_id}, ${token}, ${auth.userId}, ${team.name}, ${email}, ${now.toISOString()}, ${now.toISOString()}, ${expiresAt.toISOString()}
      ) RETURNING id, team_id, email, team_role, team_name, token, created_at, updated_at, invited_by_user_id, expires_at
    `);

    if (newInviteResult.length === 0) {
      console.error("Error creating team invitation: No result returned");
      return c.json({
        error: "Failed to create team invitation"
      }, 500);
    }

    const newInvite = newInviteResult[0];

    // Send invitation email
    try {
      // Get inviter's name from user_accounts
      const inviterResult = await db
        .select({
          firstName: userAccounts.firstName,
          lastName: userAccounts.lastName,
          email: userAccounts.email,
        })
        .from(userAccounts)
        .where(eq(userAccounts.userId, auth.userId))
        .limit(1);

      const inviter = inviterResult.length > 0 ? inviterResult[0] : null;
      const inviterName = inviter
        ? `${inviter.firstName || ''} ${inviter.lastName || ''}`.trim() || inviter.email || 'Someone'
        : 'Someone';

      // Create email service
      const emailService = createEmailService(c.env);

      // Generate invite URL (you may need to adjust this based on your frontend URL structure)
      const baseUrl = c.env.FRONTEND_URL || 'https://app.imoblr.com';
      const inviteUrl = `${baseUrl}/aceitar-convite?token=${token}&email=${email}`;

      // Format expiry date
      const expiryDate = new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'UTC'
      }).format(expiresAt);

      // Load and send the team invite template
      const template = await emailService.loadTemplate('team-invite', {
        recipientName: email.split('@')[0], // Use email prefix as fallback name
        recipientEmail: email,
        teamName: team.name || 'Unknown Team',
        inviterName: inviterName,
        inviteUrl: inviteUrl,
        expiryDate: expiryDate,
        supportEmail: '<EMAIL>',
        unsubscribeUrl: `${baseUrl}/unsubscribe/team-invites`
      });

      const emailResult = await emailService.sendEmail(template, {
        to: email,
        idempotencyKey: `team-invite-${inviteId}`
      });

      if (!emailResult.success) {
        console.error(`Failed to send invitation email to ${email}:`, emailResult.error);
        // Don't fail the invitation creation if email fails, just log it
      } else {
        console.log(`Successfully sent invitation email to ${email} with message ID: ${emailResult.messageId}`);
      }
    } catch (emailError) {
      console.error(`Error sending invitation email to ${email}:`, emailError);
      // Don't fail the invitation creation if email fails, just log it
    }

    return c.json({
      success: true,
      invitation: {
        id: newInvite.id,
        team_id: newInvite.team_id,
        email: newInvite.email,
        team_role: newInvite.team_role,
        team_name: newInvite.team_name,
        token: newInvite.token,
        created_at: newInvite.created_at,
        expires_at: newInvite.expires_at
      }
    });

  } catch (error) {
    console.error("Error creating team invitation:", error);
    return c.json({
      error: error instanceof Error ? error.message : "Failed to create team invitation"
    }, 500);
  }
};

// Helper function to generate a secure random token
function generateInviteToken(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 30; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
