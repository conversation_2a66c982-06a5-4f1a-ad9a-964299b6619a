import { Hono } from "hono/tiny";
import type { Env } from "../../types";
import { createUnauthorizedResponse, verifyAuthentication } from "../../utils";
import { 
  handleCreateTeamInvite,
  handleListTeamInvites,
  handleAcceptTeamInvite,
  handleDeleteTeamInvite,
  handleLookupTeamInvite,
  handleResendTeamInvite
} from "./handlers";

// Create Hono router with binding type for environment variables
const teamInvitesRouter = new Hono<{
  Bindings: Env;
  Variables: { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };
}>();

// Middleware: Authentication Check for most routes (except lookup and accept which have their own auth logic)
teamInvitesRouter.use("/*", async (c, next) => {
  const path = c.req.path;
  
  // Skip auth middleware for lookup and accept endpoints as they handle auth differently
  if (path.includes("/lookup/") || path.includes("/accept")) {
    return await next();
  }

  const env = c.env;

  // Pass the raw request directly to verifyAuthentication
  const auth = await verifyAuthentication(c.req.raw, env);
  if (!auth.isAuthenticated) {
    // Return the unauthorized response directly
    return createUnauthorizedResponse(auth.error || "Authentication required");
  }

  // Store auth data in the context variables for subsequent handlers/middleware
  c.set("auth", auth);

  // Proceed to the next middleware or route handler
  return await next();
});

// Define routes
teamInvitesRouter.post("/", handleCreateTeamInvite);
teamInvitesRouter.get("/:teamId", handleListTeamInvites);
teamInvitesRouter.post("/accept", handleAcceptTeamInvite);
teamInvitesRouter.post("/resend/:inviteId", handleResendTeamInvite);
teamInvitesRouter.delete("/:inviteId", handleDeleteTeamInvite);
teamInvitesRouter.get("/lookup/:token", handleLookupTeamInvite);

export default teamInvitesRouter;
