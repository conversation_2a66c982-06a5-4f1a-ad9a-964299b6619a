import type { Env } from "@/api/types";
import { getSupabaseClient } from "@/api/utils";
import { createUnauthorizedResponse } from "@/api/utils/auth";
import type { Context } from "hono";

type HandlerVariables = { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };

export async function handleDeleteWebsite(c: Context<{ Bindings: Env; Variables: HandlerVariables }>) {
  try {
    // Get auth from context (set by middleware)
    const auth = c.get("auth");

    if (!auth?.isAuthenticated || !auth.userId) {
      return createUnauthorizedResponse("Authentication required");
    }

    // Get the website ID from the URL params
    const { websiteId } = c.req.param();

    if (!websiteId) {
      return c.json({ error: "Website ID is required" }, 400);
    }

    // Get JWT token from request for user-specific operations
    const authHeader = c.req.header("Authorization");
    const jwt = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : undefined;

    // Initialize Supabase client
    const { client: supabaseClient, error: supabaseError } = getSupabaseClient(c.env, jwt);
    if (supabaseError || !supabaseClient) {
      console.error("Supabase client initialization error:", supabaseError);
      return c.json({
        error: "Server configuration error: Unable to connect to database",
        details: "Failed to initialize Supabase client"
      }, 500);
    }

    // First, check if the website exists and get its data
    const { data: existingWebsite, error: fetchError } = await supabaseClient
      .from('websites')
      .select('id, title, theme_id, team_account_id')
      .eq('id', websiteId)
      .single();

    if (fetchError) {
      console.error("Error fetching website:", fetchError);
      return c.json({
        error: "Failed to fetch website",
        details: fetchError.message
      }, 500);
    }

    if (!existingWebsite) {
      return c.json({ error: "Website not found" }, 404);
    }

    // Delete the website (this will cascade delete related records due to foreign key constraints)
    // The theme will be deleted automatically due to the ON DELETE CASCADE constraint
    const { error: deleteError } = await supabaseClient
      .from('websites')
      .delete()
      .eq('id', websiteId);

    if (deleteError) {
      console.error("Error deleting website:", deleteError);
      return c.json({
        error: "Failed to delete website",
        details: deleteError.message
      }, 500);
    }

    return c.json({
      success: true,
      message: "Website deleted successfully",
      deleted_website_id: websiteId
    });
  } catch (error) {
    console.error("Error deleting website:", error);
    return c.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : "Unknown error occurred"
    }, 500);
  }
}
