import type { Env } from "@/api/types";
import { getSupabaseClient } from "@/api/utils";
import { createUnauthorizedResponse } from "@/api/utils/auth";
import type { Context } from "hono";

interface UpdateWebsiteRequest {
  title?: string;
  subdomain?: string;
  description?: string;
  published?: boolean;
  status?: "draft" | "published" | "archived";
  logo_image_url?: string;
  hero_image_url?: string;
  institutional_image_url?: string;
  theme?: {
    primary_color?: string;
    secondary_color?: string;
    font_family?: string;
  };
}

type HandlerVariables = { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };

export async function handleUpdateWebsite(c: Context<{ Bindings: Env; Variables: HandlerVariables }>) {
  try {
    // Get auth from context (set by middleware)
    const auth = c.get("auth");

    if (!auth?.isAuthenticated || !auth.userId) {
      return createUnauthorizedResponse("Authentication required");
    }

    // Get the website ID from the URL params
    const { websiteId } = c.req.param();

    if (!websiteId) {
      return c.json({ error: "Website ID is required" }, 400);
    }

    // Get request body
    const requestData = await c.req.json<UpdateWebsiteRequest>();
    const { title, subdomain, description, published, status, logo_image_url, hero_image_url, institutional_image_url, theme } = requestData;

    // Get JWT token from request for user-specific operations
    const authHeader = c.req.header("Authorization");
    const jwt = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : undefined;

    // Initialize Supabase client
    const { client: supabaseClient, error: supabaseError } = getSupabaseClient(c.env, jwt);
    if (supabaseError || !supabaseClient) {
      console.error("Supabase client initialization error:", supabaseError);
      return c.json({
        error: "Server configuration error: Unable to connect to database",
        details: "Failed to initialize Supabase client"
      }, 500);
    }

    // First, check if the website exists and get its current data
    const { data: existingWebsite, error: fetchError } = await supabaseClient
      .from('websites')
      .select('*, theme:website_themes!websites_theme_id_fkey(*)')
      .eq('id', websiteId)
      .single();

    if (fetchError) {
      console.error("Error fetching website:", fetchError);
      return c.json({
        error: "Failed to fetch website",
        details: fetchError.message
      }, 500);
    }

    if (!existingWebsite) {
      return c.json({ error: "Website not found" }, 404);
    }

    // Prepare website updates (only include fields that are provided)
    const websiteUpdates: any = {};
    if (title !== undefined) websiteUpdates.title = title;
    if (subdomain !== undefined) websiteUpdates.subdomain = subdomain;
    if (description !== undefined) websiteUpdates.description = description;
    if (published !== undefined) websiteUpdates.published = published;
    if (status !== undefined) websiteUpdates.status = status;
    if (logo_image_url !== undefined) websiteUpdates.logo_image_url = logo_image_url;
    if (hero_image_url !== undefined) websiteUpdates.hero_image_url = hero_image_url;
    if (institutional_image_url !== undefined) websiteUpdates.institutional_image_url = institutional_image_url;

    // Update website if there are website-level changes
    if (Object.keys(websiteUpdates).length > 0) {
      const { error: websiteUpdateError } = await supabaseClient
        .from('websites')
        .update(websiteUpdates)
        .eq('id', websiteId);

      if (websiteUpdateError) {
        console.error("Error updating website:", websiteUpdateError);
        return c.json({
          error: "Failed to update website",
          details: websiteUpdateError.message
        }, 500);
      }
    }

    // Update theme if theme data is provided
    if (theme && existingWebsite.theme_id) {
      const themeUpdates: any = {};
      if (theme.primary_color !== undefined) themeUpdates.primary_color = theme.primary_color;
      if (theme.secondary_color !== undefined) themeUpdates.secondary_color = theme.secondary_color;
      if (theme.font_family !== undefined) themeUpdates.font_family = theme.font_family;

      if (Object.keys(themeUpdates).length > 0) {
        const { error: themeUpdateError } = await supabaseClient
          .from('website_themes')
          .update(themeUpdates)
          .eq('id', existingWebsite.theme_id);

        if (themeUpdateError) {
          console.error("Error updating theme:", themeUpdateError);
          return c.json({
            error: "Failed to update website theme",
            details: themeUpdateError.message
          }, 500);
        }
      }
    }

    // Fetch the updated website with theme
    const { data: updatedWebsite, error: refetchError } = await supabaseClient
      .from('websites')
      .select('*, theme:website_themes!websites_theme_id_fkey(*)')
      .eq('id', websiteId)
      .single();

    if (refetchError) {
      console.error("Error refetching updated website:", refetchError);
      return c.json({
        error: "Website updated but failed to fetch updated data",
        details: refetchError.message
      }, 500);
    }

    return c.json({
      success: true,
      website: updatedWebsite
    });
  } catch (error) {
    console.error("Error updating website:", error);
    return c.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : "Unknown error occurred"
    }, 500);
  }
}
