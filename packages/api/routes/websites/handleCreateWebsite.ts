import type { Env } from "@/api/types";
import { getSupabaseClient } from "@/api/utils";
import { createUnauthorizedResponse } from "@/api/utils/auth";
import type { Context } from "hono";

interface CreateWebsiteRequest {
  team_account_id: string;
  title: string;
  subdomain: string;
  description?: string;
  published?: boolean;
  status?: "draft" | "published" | "archived";
  theme?: {
    primary_color: string;
    secondary_color: string;
    font_family?: string;
  };
}

type HandlerVariables = { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };

export async function handleCreateWebsite(c: Context<{ Bindings: Env; Variables: HandlerVariables }>) {
  try {
    // Get auth from context (set by middleware)
    const auth = c.get("auth");

    if (!auth?.isAuthenticated || !auth.userId) {
      return createUnauthorizedResponse("Authentication required");
    }

    // Get request body
    const requestData = await c.req.json<CreateWebsiteRequest>();
    const { team_account_id, title, subdomain, description, published, status, theme } = requestData;

    // Validate required fields
    if (!team_account_id) {
      return c.json({ error: "Team account ID is required" }, 400);
    }
    // if (!title) {
    //   return c.json({ error: "Title is required" }, 400);
    // }
    if (!subdomain) {
      return c.json({ error: "Subdomain is required" }, 400);
    }

    // Get JWT token from request for user-specific operations
    const authHeader = c.req.header("Authorization");
    const jwt = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : undefined;

    // Initialize Supabase client
    const { client: supabaseClient, error: supabaseError } = getSupabaseClient(c.env, jwt);
    if (supabaseError || !supabaseClient) {
      console.error("Supabase client initialization error:", supabaseError);
      return c.json({
        error: "Server configuration error: Unable to connect to database",
        details: "Failed to initialize Supabase client"
      }, 500);
    }

    // We'll rely on RLS policies to check team access
    // If the user doesn't have access to the team, the website creation will fail

    // First, check if the subdomain already exists
    const { data: existingWebsite, error: checkError } = await supabaseClient
      .from('websites')
      .select('id')
      .eq('subdomain', subdomain)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 means no rows found, which is what we want
      console.error("Error checking subdomain:", checkError);
      return c.json({
        error: "Failed to validate subdomain",
        details: checkError.message
      }, 500);
    }

    if (existingWebsite) {
      return c.json({
        error: "Subdomain already exists",
        details: "This subdomain is already taken. Please choose a different subdomain."
      }, 400);
    }

    // Create the website
    const { data: websiteData, error: websiteError } = await supabaseClient
      .from('websites')
      .insert({
        team_account_id,
        title,
        subdomain,
        description: description || null,
        published: published || false,
        status: status || 'draft'
      })
      .select()
      .single();

    if (websiteError) {
      console.error("Error creating website:", websiteError);

      // Handle specific error cases
      if (websiteError.code === '23505' && websiteError.message.includes('websites_subdomain_key')) {
        return c.json({
          error: "Subdomain already exists",
          details: "This subdomain is already taken. Please choose a different subdomain."
        }, 400);
      }

      return c.json({
        error: "Failed to create website",
        details: websiteError.message
      }, 500);
    }

    // Then create the theme with the team_account_id and website_id
    const { data: themeData, error: themeError } = await supabaseClient
      .from('website_themes')
      .insert({
        primary_color: theme?.primary_color || '#3B82F6',
        secondary_color: theme?.secondary_color || '#10B981',
        font_family: theme?.font_family || 'Inter',
        team_account_id: team_account_id,
        website_id: websiteData.id
      })
      .select()
      .single();

    if (themeError) {
      // If theme creation fails, delete the website we just created
      await supabaseClient
        .from('websites')
        .delete()
        .eq('id', websiteData.id);

      console.error("Error creating theme:", themeError);
      return c.json({
        error: "Failed to create website theme",
        details: themeError.message
      }, 500);
    }

    // Update the website with the theme_id
    const { data: updatedWebsite, error: updateError } = await supabaseClient
      .from('websites')
      .update({ theme_id: themeData.id })
      .eq('id', websiteData.id)
      .select('*, theme:website_themes!websites_theme_id_fkey(*)')
      .single();

    if (updateError) {
      // If update fails, clean up both the website and theme
      await supabaseClient
        .from('website_themes')
        .delete()
        .eq('id', themeData.id);

      await supabaseClient
        .from('websites')
        .delete()
        .eq('id', websiteData.id);

      console.error("Error updating website with theme:", updateError);
      return c.json({
        error: "Failed to link website and theme",
        details: updateError.message
      }, 500);
    }

    const data = updatedWebsite;

    return c.json({
      success: true,
      website: data
    });
  } catch (error) {
    console.error("Error creating website:", error);
    return c.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : "Unknown error occurred"
    }, 500);
  }
}
