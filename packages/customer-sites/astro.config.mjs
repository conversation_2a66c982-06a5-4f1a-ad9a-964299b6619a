// @ts-check
import { defineConfig } from 'astro/config';
import tailwindcss from '@tailwindcss/vite';
import path from 'path';
import { fileURLToPath } from 'url';
import svgr from 'vite-plugin-svgr';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

import react from '@astrojs/react';

import cloudflare from '@astrojs/cloudflare';

// https://astro.build/config
export default defineConfig({
  output: "server",
  adapter: cloudflare(),
  integrations: [react()],
  image: {
    domains: ["wsrv.nl", "images.unsplash.com"],
    remotePatterns: [{ protocol: "https" }]
  },
  vite: {
    resolve: {
      // Use react-dom/server.edge instead of react-dom/server.browser for React 19.
      // Without this, MessageChannel from node:worker_threads needs to be polyfilled.
      alias: {
        ...(import.meta.env.PROD ? {
          "react-dom/server": "react-dom/server.edge",
        } : {}),
        "@platform/assets": path.resolve("../assets"),
      },
    },
    // Configure server for better SVG hot reloading
    server: {
      watch: {
        // Only ignore node_modules, allow watching of assets package
        ignored: ['**/node_modules/**'],
      },
      // Force reload when assets change
      hmr: {
        overlay: true,
      },
    },
    // Ensure assets are not optimized as dependencies so they can be watched
    optimizeDeps: {
      exclude: ['@platform/assets'],
    },
    plugins: [
      tailwindcss(),
      // Custom plugin to handle cross-package SVG hot reloading
      {
        name: 'svg-hot-reload',
        configureServer(server) {
          // Watch for changes in the assets package
          const assetsPath = path.resolve(__dirname, '../assets');
          console.log('Watching assets path:', assetsPath);

          // Add the entire assets directory to the watcher
          server.watcher.add(assetsPath);

          server.watcher.on('change', (file) => {
            console.log('File changed:', file);
            if (file.includes('assets') && file.endsWith('.svg')) {
              console.log('SVG file changed, triggering reload:', file);
              // Force a full reload when SVG files change
              server.ws.send({
                type: 'full-reload'
              });
            }
          });
        },
      },
      svgr({
        include: '**/*.svg?react',
        svgrOptions: {
          plugins: ['@svgr/plugin-svgo', '@svgr/plugin-jsx'],
          svgoConfig: {
            plugins: [
              {
                name: 'preset-default',
                params: {
                  overrides: {
                    // Preserve viewBox which is essential for proper scaling
                    removeViewBox: false,
                    // Don't remove unknown elements and attributes that might be important
                    removeUnknownsAndDefaults: false,
                    // Preserve attributes that might be needed for proper rendering
                    removeUselessStrokeAndFill: false,
                  },
                },
              },
              // Still remove these for optimization, but safely
              'removeTitle',
              'removeDesc',
              'removeDoctype',
              // Don't clean up IDs too aggressively as they might be referenced
              {
                name: 'cleanupIds',
                params: {
                  remove: false,
                  minify: true,
                  preserve: [],
                },
              },
            ],
          },
        },
      }),
    ],
  },

});