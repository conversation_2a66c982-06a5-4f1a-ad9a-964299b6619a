import SearchIcon from "@platform/assets/icons/search-01.svg?react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { PropertyLocationSearch } from "./PropertyLocationSearch.jsx";
import { PropertyTypeSelect } from "./PropertyTypeSelect.jsx";

// LocationSearchDropdown component
function LocationSearchDropdown({ primaryColor, teamId, onLocationSelect, searchValue }) {
  const [locations, setLocations] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // Safely access sessionStorage
  const safeSessionStorage = useMemo(
    () => ({
      getItem: (key) => {
        try {
          return typeof window !== "undefined" && typeof sessionStorage !== "undefined"
            ? sessionStorage.getItem(key)
            : null;
        } catch (e) {
          console.error("Error accessing sessionStorage:", e);
          return null;
        }
      },
      setItem: (key, value) => {
        try {
          if (typeof window !== "undefined" && typeof sessionStorage !== "undefined") {
            sessionStorage.setItem(key, value);
          }
        } catch (e) {
          console.error("Error setting sessionStorage:", e);
        }
      },
    }),
    [],
  );

  // Fetch locations from API
  useEffect(() => {
    if (!teamId) return;

    const fetchLocations = async () => {
      try {
        const sessionStorageKey = `teamLocations_${teamId}`;
        const cachedLocations = safeSessionStorage.getItem(sessionStorageKey);

        if (cachedLocations) {
          try {
            const parsedLocations = JSON.parse(cachedLocations);
            if (parsedLocations && parsedLocations.length > 0) {
              setLocations(parsedLocations);
              return;
            }
          } catch (err) {
            console.error("Error parsing cached locations:", err);
          }
        }

        setIsLoading(true);
        const response = await fetch(`/api/get-team-locations?teamId=${encodeURIComponent(teamId)}`);

        if (!response.ok) {
          throw new Error(`Failed to fetch locations: ${response.status}`);
        }

        const data = await response.json();
        if (data.locations && Array.isArray(data.locations)) {
          setLocations(data.locations);
          safeSessionStorage.setItem(sessionStorageKey, JSON.stringify(data.locations));
        }
      } catch (err) {
        console.error("Error fetching locations:", err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLocations();
  }, [teamId, safeSessionStorage]);

  // Filter locations based on search
  const filteredLocations = useMemo(() => {
    if (!searchValue.trim()) return locations;

    const searchTerm = searchValue.toLowerCase().trim();
    return locations
      .map((city) => {
        const filteredNeighborhoods = city.neighborhoods.filter(
          (neighborhood) =>
            neighborhood.name.toLowerCase().includes(searchTerm) || city.name.toLowerCase().includes(searchTerm),
        );
        return { ...city, neighborhoods: filteredNeighborhoods };
      })
      .filter((city) => city.neighborhoods.length > 0);
  }, [locations, searchValue]);

  const handleLocationClick = (locationString) => {
    onLocationSelect(locationString);
  };

  return (
    <div className="absolute top-[50%] left-0 z-50 max-h-96 w-full overflow-y-auto rounded-b-4xl border border-gray-300 bg-white/80 shadow-xl backdrop-blur-sm">
      <div className="p-4">
        {isLoading ? (
          <div className="py-4 text-center text-gray-500">Carregando...</div>
        ) : filteredLocations.length > 0 ? (
          <div className="space-y-4">
            {filteredLocations.map((city) => (
              <div key={city.id} className="space-y-2">
                <h3 className="font-semibold text-gray-800 text-sm">{city.name}</h3>
                <div className="grid grid-cols-2 gap-2">
                  {city.neighborhoods.map((neighborhood) => (
                    <button
                      key={neighborhood.id}
                      type="button"
                      onClick={() => handleLocationClick(`${neighborhood.name}, ${city.name}`)}
                      className="rounded-md px-3 py-2 text-left text-gray-600 text-sm transition-colors hover:bg-gray-100"
                    >
                      {neighborhood.name}
                    </button>
                  ))}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="py-4 text-center text-gray-500">Nenhuma localização encontrada</div>
        )}
      </div>
    </div>
  );
}

export default function PropertySearchTabs({ primaryColor, headingColor, teamId, websiteAddress }) {
  const [activeTab, setActiveTab] = useState("alugar");
  const [tabMeasurements, setTabMeasurements] = useState({});
  const [indicatorStyle, setIndicatorStyle] = useState({});
  const [propertyCode, setPropertyCode] = useState("");
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [isLocationSearchFocused, setIsLocationSearchFocused] = useState(false);
  const [locationSearchValue, setLocationSearchValue] = useState("");
  const containerRef = useRef(null);

  // Handle location selection and clear search
  const handleLocationSelect = useCallback((location) => {
    setSelectedLocation(location);
    setLocationSearchValue(""); // Clear search when location is selected
    setIsLocationSearchFocused(false); // Hide dropdown
  }, []);
  const alugarRef = useRef(null);
  const comprarRef = useRef(null);
  const codigoRef = useRef(null);

  // Debug logging
  useEffect(() => {
    console.log("PropertySearchTabs: Component mounted with props:", {
      primaryColor,
      headingColor,
      teamId,
      websiteAddress,
      activeTab,
      timestamp: new Date().toISOString(),
    });

    return () => {
      console.log("PropertySearchTabs: Component unmounting", {
        timestamp: new Date().toISOString(),
      });
    };
  }, [primaryColor, headingColor, teamId, websiteAddress, activeTab]);

  useEffect(() => {
    console.log("PropertySearchTabs: Props updated:", {
      primaryColor,
      headingColor,
      teamId,
      websiteAddress,
      activeTab,
      timestamp: new Date().toISOString(),
    });
  }, [primaryColor, headingColor, teamId, websiteAddress, activeTab]);

  // Measure tab positions and update indicator
  const measureTabs = useCallback(() => {
    if (alugarRef.current && comprarRef.current && codigoRef.current && containerRef.current) {
      // Find the inner tab container (the one with the background)
      const tabContainer = containerRef.current.querySelector("[data-tab-container]");
      if (!tabContainer) return false;

      const tabContainerRect = tabContainer.getBoundingClientRect();
      const alugarRect = alugarRef.current.getBoundingClientRect();
      const comprarRect = comprarRef.current.getBoundingClientRect();
      const codigoRect = codigoRef.current.getBoundingClientRect();

      // Check if elements have valid dimensions (not zero)
      if (alugarRect.width > 0 && comprarRect.width > 0 && codigoRect.width > 0 && tabContainerRect.width > 0) {
        const measurements = {
          alugar: {
            left: alugarRect.left - tabContainerRect.left,
            width: alugarRect.width,
          },
          comprar: {
            left: comprarRect.left - tabContainerRect.left,
            width: comprarRect.width,
          },
          codigo: {
            left: codigoRect.left - tabContainerRect.left,
            width: codigoRect.width,
          },
        };

        setTabMeasurements(measurements);
        return true; // Success
      }
    }
    return false; // Failed
  }, []);

  // Measure tabs with retry logic
  const measureTabsWithRetry = useCallback(
    (retries = 3) => {
      const attempt = () => {
        const success = measureTabs();
        if (!success && retries > 0) {
          // Check if we're on a smaller screen (where the issue occurs)
          const isSmallScreen = window.innerWidth < 768; // md breakpoint
          const delay = isSmallScreen ? 100 : 50; // Longer delay for small screens

          // Retry after a delay
          setTimeout(() => measureTabsWithRetry(retries - 1), delay);
        }
      };

      // Use requestAnimationFrame to ensure DOM is ready
      requestAnimationFrame(attempt);
    },
    [measureTabs],
  );

  // Update indicator position when active tab changes
  useEffect(() => {
    if (tabMeasurements[activeTab]) {
      setIndicatorStyle({
        left: `${tabMeasurements[activeTab].left}px`,
        width: `${tabMeasurements[activeTab].width}px`,
      });
    }
  }, [activeTab, tabMeasurements]);

  // Measure tabs on mount and when window resizes
  useEffect(() => {
    const handleResize = () => {
      const isSmallScreen = window.innerWidth < 768;

      if (isSmallScreen) {
        // For small screens, wait a bit longer and retry more times
        setTimeout(() => {
          measureTabsWithRetry(5); // More retries for small screens
        }, 150); // Initial delay for small screens
      } else {
        measureTabsWithRetry(3); // Normal behavior for larger screens
      }
    };

    // Initial measurement with small screen awareness
    handleResize();

    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, [measureTabsWithRetry]);

  const handleTabClick = (tab) => {
    setActiveTab(tab);
    // Update global variable for search functionality
    window.__selectedTransactionType = tab;

    // Trigger measurement if we don't have valid measurements yet
    if (!tabMeasurements[tab] || tabMeasurements[tab].width === 0) {
      const isSmallScreen = window.innerWidth < 768;
      if (isSmallScreen) {
        measureTabsWithRetry(3); // More retries for small screens
      } else {
        measureTabsWithRetry(1);
      }
    }
  };

  const handleSearch = () => {
    let searchUrl = `/${websiteAddress}/imoveis`;
    const params = new URLSearchParams();

    if (activeTab === "codigo") {
      // Search by property code
      if (propertyCode.trim()) {
        params.append("code", propertyCode.trim());
      }
    } else {
      // Regular search by location and type
      const selectedLocation = window.__selectedLocation;
      const selectedPropertyType = window.__selectedPropertyType;
      const transactionType = activeTab;

      if (selectedLocation && selectedLocation !== "all") {
        params.append("location", selectedLocation);
      }
      if (selectedPropertyType && selectedPropertyType !== "all") {
        params.append("type", selectedPropertyType);
      }
      if (transactionType) {
        params.append("transaction", transactionType);
      }
    }

    if (params.toString()) {
      searchUrl += `?${params.toString()}`;
    }

    window.location.href = searchUrl;
  };

  return (
    <div className="z-50 w-full max-w-5xl px-4">
      {/* Tab Container */}
      <div ref={containerRef} className="relative mb-6 flex items-center justify-center gap-2">
        <div className="rounded-full bg-white/40 backdrop-blur-xs" data-tab-container>
          {/* Morphing Background */}
          <div
            className="absolute rounded-full shadow-[0_6px_12px_rgba(0,0,0,0.25)] transition-all duration-300 ease-in-out"
            style={{
              backgroundColor: primaryColor || "#3B82F6",
              height: "100%",
              ...indicatorStyle,
            }}
          />

          {/* Tab Buttons */}
          <button
            ref={alugarRef}
            type="button"
            className="relative z-10 cursor-pointer rounded-md px-4 py-3 font-medium text-sm text-white transition-all duration-300 ease-in-out focus:outline-none"
            onClick={() => handleTabClick("alugar")}
          >
            Alugar
          </button>
          <button
            ref={comprarRef}
            type="button"
            className="relative z-10 cursor-pointer rounded-md px-4 py-3 font-medium text-sm text-white transition-all duration-300 ease-in-out focus:outline-none"
            onClick={() => handleTabClick("comprar")}
          >
            Comprar
          </button>
          <button
            ref={codigoRef}
            type="button"
            className="relative z-10 cursor-pointer rounded-md px-4 py-3 font-medium text-sm text-white transition-all duration-300 ease-in-out focus:outline-none"
            onClick={() => handleTabClick("codigo")}
          >
            Buscar por código
          </button>
        </div>
      </div>

      {/* Search Fields */}
      <div className="relative flex h-16 w-full items-center">
        {/* Dropdown for location search - only visible when focused */}
        {isLocationSearchFocused && activeTab !== "codigo" && (
          <LocationSearchDropdown
            primaryColor={primaryColor}
            teamId={teamId}
            onLocationSelect={handleLocationSelect}
            searchValue={locationSearchValue}
          />
        )}

        <div className="relative flex h-16 w-full flex-row items-center justify-between gap-4 rounded-full bg-background shadow-[0_4px_16px_rgba(0,0,0,0.25)]">
          {activeTab === "codigo" ? (
            /* Property Code Input */
            <input
              type="text"
              value={propertyCode}
              onChange={(e) => setPropertyCode(e.target.value)}
              placeholder="Exemplo: IMV-412042"
              className="flex-1 rounded-full border-none bg-transparent px-4 py-3 text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-opacity-50"
              style={{ focusRingColor: primaryColor }}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleSearch();
                }
              }}
            />
          ) : (
            <div className="flex flex-1 flex-row">
              {/* Location Field */}
              <PropertyLocationSearch
                primaryColor={primaryColor}
                teamId={teamId || ""}
                selectedLocation={selectedLocation}
                onFocus={() => setIsLocationSearchFocused(true)}
                onBlur={() => setIsLocationSearchFocused(false)}
                onLocationSelect={handleLocationSelect}
                onSearchChange={setLocationSearchValue}
              />

              {/* Property Type Field */}
              <PropertyTypeSelect
                primaryColor={primaryColor}
                teamId={teamId || ""}
                websiteAddress={websiteAddress || ""}
              />
            </div>
          )}

          {/* Search Button */}
          <button
            type="button"
            className="mr-1 flex h-14 w-14 cursor-pointer items-center justify-center rounded-full font-medium text-white transition-colors"
            style={{ backgroundColor: primaryColor }}
            onMouseOver={(e) => {
              e.target.style.backgroundColor = headingColor;
            }}
            onFocus={(e) => {
              e.target.style.backgroundColor = headingColor;
            }}
            onMouseOut={(e) => {
              e.target.style.backgroundColor = primaryColor;
            }}
            onBlur={(e) => {
              e.target.style.backgroundColor = primaryColor;
            }}
            onClick={handleSearch}
            aria-label="Search properties"
          >
            <SearchIcon className="pointer-events-none h-5 w-5" />
          </button>
        </div>
      </div>
    </div>
  );
}
