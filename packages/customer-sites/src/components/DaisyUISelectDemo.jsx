
import { PropertyLocationSelect } from "./PropertyLocationSearch";
import { PropertyTypeSelect } from "./PropertyTypeSelect";

/**
 * Demo component showing both DaisyUI implementations
 *
 * Usage Examples:
 *
 * 1. DaisyUI Select Component (https://daisyui.com/components/select/):
 *    <PropertyLocationSelect variant="select" teamId="your-team-id" primaryColor="#your-color" />
 *    <PropertyTypeSelect variant="select" teamId="your-team-id" primaryColor="#your-color" />
 *
 * 2. DaisyUI Dropdown Component (https://daisyui.com/components/dropdown/):
 *    <PropertyLocationSelect variant="dropdown" teamId="your-team-id" primaryColor="#your-color" />
 *    <PropertyTypeSelect variant="dropdown" teamId="your-team-id" primaryColor="#your-color" />
 *
 *    Or simply omit variant (defaults to dropdown):
 *    <PropertyLocationSelect teamId="your-team-id" primaryColor="#your-color" />
 *    <PropertyTypeSelect teamId="your-team-id" primaryColor="#your-color" />
 */
export function DaisyUISelectDemo({ teamId, primaryColor = "#3b82f6" }) {
  return (
    <div className="space-y-8 p-6">
      <div>
        <h2 className="mb-4 font-bold text-2xl">DaisyUI Select Components Demo</h2>
        <p className="mb-6 text-gray-600">Both components now support DaisyUI Select and Dropdown implementations.</p>
      </div>

      {/* DaisyUI Select Implementation */}
      <div className="space-y-4">
        <h3 className="font-semibold text-xl">DaisyUI Select Implementation</h3>
        <p className="text-gray-600 text-sm">
          Using <code className="rounded bg-gray-100 px-2 py-1">variant="select"</code> - Based on{" "}
          <a
            href="https://daisyui.com/components/select/"
            className="text-blue-600 underline"
            target="_blank"
            rel="noopener noreferrer"
          >
            DaisyUI Select Component
          </a>
        </p>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <label className="mb-2 block font-medium text-sm">Property Location (Select)</label>
            <PropertyLocationSelect variant="select" teamId={teamId} primaryColor={primaryColor} />
          </div>

          <div>
            <label className="mb-2 block font-medium text-sm">Property Type (Select)</label>
            <PropertyTypeSelect variant="select" teamId={teamId} primaryColor={primaryColor} />
          </div>
        </div>
      </div>

      {/* DaisyUI Dropdown Implementation */}
      <div className="space-y-4">
        <h3 className="font-semibold text-xl">DaisyUI Dropdown Implementation</h3>
        <p className="text-gray-600 text-sm">
          Using default variant (dropdown) - Based on{" "}
          <a
            href="https://daisyui.com/components/dropdown/"
            className="text-blue-600 underline"
            target="_blank"
            rel="noopener noreferrer"
          >
            DaisyUI Dropdown Component
          </a>
        </p>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <label className="mb-2 block font-medium text-sm">Property Location (Dropdown)</label>
            <PropertyLocationSelect teamId={teamId} primaryColor={primaryColor} />
          </div>

          <div>
            <label className="mb-2 block font-medium text-sm">Property Type (Dropdown)</label>
            <PropertyTypeSelect teamId={teamId} primaryColor={primaryColor} />
          </div>
        </div>
      </div>

      {/* Usage Instructions */}
      <div className="rounded-lg bg-gray-50 p-4">
        <h4 className="mb-2 font-semibold">Usage Instructions:</h4>
        <ul className="space-y-1 text-gray-700 text-sm">
          <li>
            • <strong>Select variant:</strong> Traditional HTML select with DaisyUI styling
          </li>
          <li>
            • <strong>Dropdown variant:</strong> Custom dropdown with better UX and styling
          </li>
          <li>• Both variants maintain the same API and functionality</li>
          <li>• The dropdown variant provides better mobile experience</li>
          <li>• Both are fully accessible and keyboard navigable</li>
        </ul>
      </div>
    </div>
  );
}
