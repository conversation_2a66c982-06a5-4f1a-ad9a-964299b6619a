---
// Import the formatToPhone function from brazilian-values
import { formatToPhone } from "brazilian-values";
import Container from "./Container.astro";

// Access the team profile data from Astro.locals
const { teamProfile, websiteInfo, websiteTheme } = Astro.locals;

// Format WhatsApp number for link (remove non-digits)
const whatsappLink = teamProfile?.whatsapp_number
    ? `https://wa.me/${teamProfile.whatsapp_number.replace(/\D/g, "")}`
    : null;

// Get the logo image URL if available from the website object
const logoImageUrl = websiteInfo?.website?.logo_image_url;

// Format the phone number for display
const formattedPhoneNumber = teamProfile?.phone_number
    ? formatToPhone(teamProfile.phone_number.replace(/\D/g, ""))
    : null;

// Format the WhatsApp number for display
const formattedWhatsappNumber = teamProfile?.whatsapp_number
    ? formatToPhone(teamProfile.whatsapp_number.replace(/\D/g, ""))
    : null;
---

<header class="relative border-b border-gray-100 bg-white">
    <Container>
        <nav
            class="hidden lg:flex top-0 left-0 h-20 right-0 justify-between items-center bg-white z-50"
        >
            <div>
                <a href="/" class="block">
                    {
                        logoImageUrl ? (
                            <img
                                src={logoImageUrl}
                                alt={
                                    teamProfile?.name ||
                                    websiteInfo?.title ||
                                    "Logo"
                                }
                                class="h-16 max-w-[200px] object-contain"
                            />
                        ) : (
                            <div class="text-2xl font-bold text-gray-600">
                                {teamProfile?.name || websiteInfo?.title}
                            </div>
                        )
                    }
                </a>
            </div>
            <div class="flex items-center gap-4">
                <a href="/" class="text-primary hover:underline">Início</a>
                <a href="/imoveis" class="text-primary hover:underline"
                    >Imóveis</a
                >
                <a href="/sobre" class="text-primary hover:underline"
                    >Sobre Nós</a
                >
                <!-- <a href="/contato" class="text-primary hover:underline">Anuncie seu imóvel</a> -->
                <!-- {teamProfile?.phone_number && <a href={`tel:${teamProfile?.phone_number?.replace(/\D/g, '') || '+3197823158'}`} class="text-primary flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                    </svg>
                    {formattedPhoneNumber}
                </a>} -->
                {
                    whatsappLink && (
                        <a
                            href={whatsappLink}
                            class="text-primary flex items-center gap-2 ml-8"
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-5 w-5"
                                fill="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.255-.462-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.297-.347.446-.521.151-.172.2-.296.3-.495.099-.198.05-.372-.025-.521-.075-.148-.669-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z" />
                            </svg>
                            {formattedWhatsappNumber || "WhatsApp"}
                        </a>
                    )
                }
            </div>
            <button
                class="text-white text-sm bg-primary px-5 py-3 rounded-full cursor-pointer hover:opacity-90"
                style={`background-color: ${websiteTheme?.primary_color};`}
            >
                Encontre o seu imóvel
            </button>
        </nav>
        <nav
            class="flex lg:hidden top-0 left-0 h-16 right-0 justify-between items-center bg-white z-50 container mx-auto"
        >
            <div>
                <a href="/" class="block">
                    {
                        logoImageUrl ? (
                            <img
                                src={logoImageUrl}
                                alt={
                                    teamProfile?.name ||
                                    websiteInfo?.title ||
                                    "Logo"
                                }
                                class="h-10 max-w-[200px] object-contain"
                            />
                        ) : (
                            <div class="text-2xl font-bold text-gray-600">
                                {teamProfile?.name || websiteInfo?.title}
                            </div>
                        )
                    }
                </a>
            </div>
            <button
                id="mobile-menu-toggle"
                class="py-2 px-3 text-gray-600 hover:text-gray-900 focus:outline-none relative z-[101] flex flex-row items-center justify-center gap-2 text-xs rounded-full border border-gray-200"
            >
                MENU
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>
        </nav>
    </Container>
</header>
