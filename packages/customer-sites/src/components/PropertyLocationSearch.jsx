import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";

function PropertyLocationSearchComponent({
  primaryColor,
  teamId,
  onFocus,
  onBlur,
  onLocationSelect,
  selectedLocation,
  onSearchChange,
}) {
  console.log("PropertyLocationSearch: Component initialized with teamId:", teamId, "primaryColor:", primaryColor);

  const [_locations, setLocations] = useState([]);
  const [_isLoading, setIsLoading] = useState(false);
  const [_error, setError] = useState(null);
  const [searchValue, setSearchValue] = useState("");
  const [_isFocused, setIsFocused] = useState(false);
  const inputRef = useRef(null);

  // Handle input focus
  const handleFocus = useCallback(() => {
    console.log("PropertyLocationSearch: Input focus triggered");
    setIsFocused(true);
    if (onFocus) {
      onFocus();
    }
  }, [onFocus]);

  // Handle input blur
  const handleBlur = useCallback(() => {
    console.log("PropertyLocationSearch: Input blur triggered");
    // Delay blur to allow for click events on dropdown items
    setTimeout(() => {
      console.log("PropertyLocationSearch: Executing blur after delay");
      setIsFocused(false);
      if (onBlur) {
        onBlur();
      }
    }, 150);
  }, [onBlur]);

  // Handle search input change
  const handleSearchChange = useCallback(
    (e) => {
      const value = e.target.value;
      setSearchValue(value);
      if (onSearchChange) {
        onSearchChange(value);
      }
    },
    [onSearchChange],
  );

  // Check if we're in a browser environment where sessionStorage is available
  const isBrowser = typeof window !== "undefined" && typeof sessionStorage !== "undefined";

  // Safely access sessionStorage - memoized to prevent recreation on every render
  const safeSessionStorage = useMemo(
    () => ({
      getItem: (key) => {
        try {
          return isBrowser ? sessionStorage.getItem(key) : null;
        } catch (e) {
          console.error("Error accessing sessionStorage:", e);
          return null;
        }
      },
      setItem: (key, value) => {
        try {
          if (isBrowser) {
            sessionStorage.setItem(key, value);
          }
        } catch (e) {
          console.error("Error setting sessionStorage:", e);
        }
      },
    }),
    [isBrowser],
  );

  // Fetch locations from API
  useEffect(() => {
    if (!teamId) {
      console.log("PropertyLocationSearch: No teamId provided, skipping location fetch");
      return;
    }

    const fetchLocations = async () => {
      console.log("PropertyLocationSearch: Fetching locations for teamId:", teamId);

      try {
        // Check if we have cached locations for this team
        const sessionStorageKey = `teamLocations_${teamId}`;
        let cachedLocations;

        // Use our safe wrapper to access sessionStorage
        cachedLocations = safeSessionStorage.getItem(sessionStorageKey);

        if (cachedLocations) {
          try {
            const parsedLocations = JSON.parse(cachedLocations);

            // Only use cached locations if they're not empty
            if (parsedLocations && parsedLocations.length > 0) {
              setLocations(parsedLocations);
              return;
            }
          } catch (err) {
            console.error("Error parsing cached locations:", err);
            // Continue with fetching if parsing fails
          }
        }

        setIsLoading(true);
        setError(null);

        const response = await fetch(`/api/get-team-locations?teamId=${encodeURIComponent(teamId)}`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch locations: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.locations || !Array.isArray(data.locations)) {
          console.warn("PropertyLocationSearch: No locations found or invalid response format");
          setLocations([]);
          setIsLoading(false);
          return;
        }

        // Keep the original structure with cities and neighborhoods
        setLocations(data.locations);

        // Store in sessionStorage to avoid redundant API calls
        safeSessionStorage.setItem(sessionStorageKey, JSON.stringify(data.locations));
      } catch (err) {
        console.error("PropertyLocationSearch: Error fetching locations:", err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    // Call the fetch function
    fetchLocations();
  }, [teamId, safeSessionStorage.getItem, safeSessionStorage.setItem]);

  // Render the search input
  return (
    <div className="relative flex flex-1">
      <div className="pointer-events-none absolute inset-y-0 left-3 z-10 flex w-full items-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5 text-gray-400"
          viewBox="0 0 20 20"
          fill="currentColor"
          aria-hidden="true"
        >
          <path
            fillRule="evenodd"
            d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
            clipRule="evenodd"
          />
        </svg>
      </div>

      {selectedLocation ? (
        // Show selected location as a tag
        <div className="flex flex-1 items-center py-4 pr-4 pl-10">
          <div
            className="inline-flex items-center gap-2 rounded-full px-3 py-1 font-medium text-sm text-white"
            style={{ backgroundColor: primaryColor }}
          >
            <span>{selectedLocation}</span>
            <button
              type="button"
              onClick={() => {
                // Set the global variable for the search button to use
                if (typeof window !== "undefined") {
                  window.__selectedLocation = null;
                }
                if (onLocationSelect) {
                  onLocationSelect(null);
                }
              }}
              className="ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full hover:bg-white/20"
              aria-label="Remove location"
            >
              <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                <path
                  fillRule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>
        </div>
      ) : (
        // Show search input
        <input
          ref={inputRef}
          type="text"
          value={searchValue}
          onChange={handleSearchChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder="Localização"
          className="flex-1 rounded-full border-none bg-transparent py-4 pr-4 pl-10 text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-opacity-50"
          style={{ "--tw-ring-color": primaryColor }}
        />
      )}
    </div>
  );
}

// Export a memoized version of the component to prevent unnecessary re-renders
export const PropertyLocationSearch = memo(PropertyLocationSearchComponent);

// Export filtered locations and handlers for parent component to use
export { PropertyLocationSearchComponent };
