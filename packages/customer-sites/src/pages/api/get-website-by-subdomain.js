import { createClient } from '@supabase/supabase-js';

export async function GET({ request, locals }) {
  try {
    const url = new URL(request.url);
    const subdomain = url.searchParams.get('subdomain');

    // Initialize Supabase client with service role key to bypass RLS
    const runtime = locals.runtime;
    const supabaseUrl = (runtime?.env?.SUPABASE_URL) || import.meta.env.SUPABASE_URL;
    const supabaseServiceKey = (runtime?.env?.SUPABASE_SERVICE_ROLE_KEY) || import.meta.env.SUPABASE_SERVICE_ROLE_KEY;

    // Check if we have the required environment variables
    if (!supabaseUrl || !supabaseServiceKey) {
      return new Response(
        JSON.stringify({
          error: 'Server configuration error',
          details: 'Missing Supabase credentials in environment variables'
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    let websiteData = null;

    // Query based on subdomain
    if (subdomain && subdomain !== 'default') {
      const { data, error } = await supabase
        .from('websites')
        .select('*, theme:website_themes!websites_theme_id_fkey(*)')
        .eq('subdomain', subdomain)
        .single();

      if (!error && data) {
        websiteData = data;
      }
    }

    // Return the website data or 404 if not found
    if (!websiteData) {
      return new Response(
        JSON.stringify({
          error: 'Subdomain not found',
          subdomain: subdomain
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Check if the website is published
    if (!websiteData.published) {
      return new Response(
        JSON.stringify({
          error: 'Website not published',
          subdomain: subdomain,
          message: 'The website exists but is not published'
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Success response
    return new Response(
      JSON.stringify(websiteData),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=60, stale-while-revalidate=300'
        }
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({
        error: 'Failed to fetch website information',
        message: error.message
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}
