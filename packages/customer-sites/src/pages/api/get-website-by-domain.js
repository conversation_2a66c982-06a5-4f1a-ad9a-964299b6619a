import { createClient } from '@supabase/supabase-js';

export async function GET({ request, locals }) {
  try {
    // Get the domain from the query parameters
    const url = new URL(request.url);
    const domain = url.searchParams.get('domain');

    if (!domain) {
      return new Response(
        JSON.stringify({ error: 'Domain parameter is required' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Initialize Supabase client with service role key to bypass RLS
    const runtime = locals.runtime;
    const supabaseUrl = (runtime?.env?.SUPABASE_URL) || import.meta.env.SUPABASE_URL;
    const supabaseServiceKey = (runtime?.env?.SUPABASE_SERVICE_ROLE_KEY) || import.meta.env.SUPABASE_SERVICE_ROLE_KEY;

    // Check if we have the required environment variables
    if (!supabaseUrl || !supabaseServiceKey) {
      return new Response(
        JSON.stringify({
          error: 'Server configuration error',
          details: 'Missing Supabase credentials in environment variables'
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Try different formats of the domain
    const domainVariations = [
      domain,
      domain.toLowerCase(),
      domain.trim(),
      // Add www. if it doesn't have it
      !domain.startsWith('www.') ? `www.${domain}` : domain,
      // Remove www. if it has it
      domain.startsWith('www.') ? domain.substring(4) : domain
    ];

    // Try to find a match with any of the domain variations
    let domainData = null;

    for (const domainVariation of domainVariations) {
      const result = await supabase
        .from('custom_domains')
        .select('website_id')
        .eq('domain_name', domainVariation)
        .maybeSingle();

      if (result.data) {
        domainData = result.data;
        break;
      }
    }

    if (!domainData) {
      return new Response(
        JSON.stringify({
          error: 'Domain not found',
          domain: domain
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Get the website data
    const { data: website, error: websiteError } = await supabase
      .from('websites')
      .select('*, theme:website_themes!websites_theme_id_fkey(*)')
      .eq('id', domainData.website_id)
      .single();

    if (websiteError) {
      // Check if the error is because no rows were found
      if (websiteError.code === 'PGRST116' ||
          (websiteError.details?.includes('0 rows'))) {
        return new Response(
          JSON.stringify({
            error: 'Website not found',
            domain: domain,
            message: 'Domain exists but the associated website could not be found'
          }),
          {
            status: 404,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }if (websiteError.code === 'PGRST409' ||
                (websiteError.message?.includes('permission denied'))) {
        return new Response(
          JSON.stringify({
            error: 'Website not available',
            domain: domain,
            message: 'The website exists but is not published or you do not have permission to view it'
          }),
          {
            status: 404,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }

      return new Response(
        JSON.stringify({
          error: 'Failed to fetch website',
          message: websiteError.message
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Check if the website is published
    if (!website.published) {
      return new Response(
        JSON.stringify({
          error: 'Website not published',
          domain: domain,
          message: 'The website exists but is not published'
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    return new Response(
      JSON.stringify({
        id: website.id,
        title: website.title,
        description: website.description,
        subdomain: website.subdomain,
        domain: domain,
        team_account_id: website.team_account_id,
        website: website,
        // Include theme data directly for easier access
        theme: website.theme || {
          primary_color: '#3B82F6',
          secondary_color: '#10B981',
          font_family: 'Poppins'
        }
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=60, stale-while-revalidate=300' // Cache for 1 minute
        }
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({
        error: 'Failed to fetch website information',
        message: error.message
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}
