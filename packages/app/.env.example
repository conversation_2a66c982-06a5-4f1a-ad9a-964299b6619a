# Supabase
# https://app.supabase.com/project/_/settings/api
EXPO_PUBLIC_SUPABASE_URL=your-project.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# API
EXPO_PUBLIC_API_URL=http://localhost:4000

# Site URL
# Used to generate invitation links and billing return URLs
# It should be the hostname of your application with protocol (e.g. https://example.com)
EXPO_PUBLIC_SITE_URL=http://localhost:8081

# Stripe
# https://dashboard.stripe.com/test/apikeys
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=your-dev-stripe-publishable-key