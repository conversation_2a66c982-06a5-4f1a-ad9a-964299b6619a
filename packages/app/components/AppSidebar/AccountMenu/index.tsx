import { Box, Center, Heading, Icon, Text } from "../../ui";
import ChevronDownIcon from "@/assets/icons/chevron-down.svg";
import MoreIcon from "@/assets/icons/more-vertical.svg";
import UserSwitchIcon from "@/assets/icons/user-switch.svg";
import UserStarIcon from "@/assets/icons/user-star-02.svg";
import UserAccountIcon from "@/assets/icons/user-account.svg";
import InvoiceIcon from "@/assets/icons/invoice-01.svg";
import TeamIcon from "@/assets/icons/user-multiple-02.svg";
import LogoutIcon from "@/assets/icons/logout-03.svg";
import { usePersonalAccount, useTeamAccount, useTeamAccountsList, useSwitchTeamAccount } from "@/hooks";
import TeamCard from "@/components/TeamCard";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import TeamPicker from "@/components/TeamPicker";
import { Fragment, useState } from "react";
import { Link } from "expo-router";
import { useSupabaseAuth } from "@/hooks/useSupabaseAuth";

interface AccountMenuProps {
  accountName?: string;
  staffCount?: number;
  onPress?: () => void;
}

const AccountMenu = ({ accountName, staffCount, onPress }: AccountMenuProps) => {
  const { signOut } = useSupabaseAuth();
  const switchTeamAccount = useSwitchTeamAccount();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const {
    data: currentTeamAccount,
    isLoading: isCurrentTeamAccountLoading,
    error: currentTeamAccountError,
  } = useTeamAccount();

  const {
    data: personalAccount,
    isLoading: isPersonalAccountLoading,
    error: personalAccountError,
  } = usePersonalAccount();

  const handleLogout = async () => {
    await signOut();
    switchTeamAccount(null);
  };

  return (
    <Fragment>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Box className="z-50 cursor-pointer flex-row items-center px-4 py-3">
            <Box className="flex-1 flex-row items-center gap-3">
              {currentTeamAccount ? (
                <TeamCard
                  darkMode
                  name={currentTeamAccount.name}
                  role={currentTeamAccount.role}
                  avatarUrl={currentTeamAccount.photo_url}
                />
              ) : (
                <Box className="h-8 w-8 rounded-lg bg-primary-600" />
              )}
            </Box>
            <Center className="right-3.5 absolute h-4 w-4">
              <ChevronDownIcon className="h-4 w-4 text-text-tertiary" />
            </Center>
          </Box>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56" alignOffset={4} sideOffset={4}>
          <DropdownMenuLabel>Meu negócio</DropdownMenuLabel>
          <DropdownMenuGroup>
            <Link href={"/configuracoes"} asChild replace={false}>
              <DropdownMenuItem>
                <UserAccountIcon className="mr-2 h-4 w-4 text-text-secondary" />
                <Text>Configurações</Text>
              </DropdownMenuItem>
            </Link>
            <Link href={"/configuracoes/faturamento"} asChild replace={false}>
              <DropdownMenuItem>
                <InvoiceIcon className="mr-2 h-4 w-4 text-text-secondary" />
                <Text>Assinatura e cobranças</Text>
              </DropdownMenuItem>
            </Link>
            <Link href={"/configuracoes/colaboradores"} asChild replace={false}>
              <DropdownMenuItem>
                <TeamIcon className="mr-2 h-4 w-4 text-text-secondary" />
                <Text>Colaboradores</Text>
              </DropdownMenuItem>
            </Link>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuItem onPress={() => setIsModalOpen(true)}>
            <UserSwitchIcon className="mr-2 h-4 w-4 text-text-secondary" />
            <Text>Gerenciar outro negócio</Text>
          </DropdownMenuItem>
          {/* <DropdownMenuSeparator /> */}
          {/* <DropdownMenuItem>
            <UserStarIcon className="mr-2 h-4 w-4 text-text-secondary" />
            <Text>Gerenciar conta pessoal</Text>
          </DropdownMenuItem> */}
          <DropdownMenuSeparator />
          <DropdownMenuItem onPress={handleLogout}>
            <LogoutIcon className="mr-2 h-4 w-4 text-destructive-800" />
            <Text className="text-destructive-800">Sair da conta</Text>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <TeamPicker isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />
    </Fragment>
  );
};

export default AccountMenu;
