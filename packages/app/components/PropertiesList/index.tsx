
import { PageRow } from "@/components/PageLayout";
import { Box, Text } from "@/components/ui";
import { useDrawers } from "@/context/drawer";
import { usePropertiesList } from "@/hooks";
import type { Property } from "@/types";
import { MasonryFlashList } from "@shopify/flash-list";
import { router } from "expo-router";
import { useMemo, useState } from "react";
import { useWindowDimensions } from "react-native";
import PropertyCard from "./PropertyCard";

export const PropertiesList = () => {
  const { openCreatePropertyDrawer } = useDrawers();
  const [_selectedProperties, setSelectedProperties] = useState<string[]>([]);
  const { data: properties, isLoading, error } = usePropertiesList();
  const [showPropertyList, _setShowPropertyList] = useState(true);
  const { width } = useWindowDimensions();

  // Calculate number of columns based on screen width
  const numColumns = useMemo(() => {
    if (width >= 1600) return 4; // xl breakpoint at 1600px
    if (width >= 1024) return 3; // lg breakpoint at 1024px
    if (width >= 640) return 2; // sm breakpoint at 640px
    return 1;
  }, [width]);

  const _handlePropertySelect = (propertyId: string) => {
    setSelectedProperties((prev) =>
      prev.includes(propertyId) ? prev.filter((id) => id !== propertyId) : [...prev, propertyId],
    );
  };

  if (error) {
    return (
      <PageRow className="mb-12">
        <Box className="mt-16 w-full items-center justify-center rounded-lg border border-border py-8">
          <Text className="text-destructive">Failed to load properties. Please try again.</Text>
          <Text className="mt-2 text-sm text-text-tertiary">{error.message}</Text>
        </Box>
      </PageRow>
    );
  }

  return (
    <PageRow className="mb-12">
      <Box className="w-full">
        <Box className="flex-1 flex-row">
          {showPropertyList && properties && properties.length > 0 && (
            <MasonryFlashList
              estimatedItemSize={260}
              data={properties}
              numColumns={numColumns}
              renderItem={({ item: property, index }: { item: Property; index: number }) => (
                <PropertyCard
                  key={`property-list-${property.id}-${index}`}
                  property={property}
                  index={index}
                  numColumns={numColumns}
                  onPress={(propertyId) => router.push(`/imoveis/${propertyId}`)}
                />
              )}
            />
          )}
        </Box>
      </Box>
    </PageRow>
  );
};

export default PropertiesList;
