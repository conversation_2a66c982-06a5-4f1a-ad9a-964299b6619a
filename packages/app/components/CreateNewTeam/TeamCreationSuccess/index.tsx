import ArrrowIcon from "@/assets/icons/arrow-right.svg";
import { Box, Button, Heading, Text } from "@/components/ui";
import { router } from "expo-router";
import LottieView from "lottie-react-native";
import type { FC } from "react";

interface TeamCreationSuccessProps {
  teamId: string | null;
  onClose?: () => void;
}

const TeamCreationSuccess: FC<TeamCreationSuccessProps> = ({ teamId, onClose }) => {
  return (
    <Box className="h-[85vh] bg-background p-8">
      <Box className="items-center p-8">
        <Box className="mb-16 h-[280px] max-h-[100%] w-[280px] max-w-[100%]">
          <LottieView
            source={require("@/assets/animations/success_clean_purple.json")}
            style={{ width: "100%", height: "100%" }}
            autoPlay={true}
            loop={false}
          />
        </Box>

        <Heading size="lg" className="mb-4 text-center">
          Seu perfil de negócio foi criado!
        </Heading>
        <Text className="w-[60%] text-center text-sm text-text-tertiary">
          Cadastramos o seu negócio com sucesso. Você já pode acessar o dashboard e começar a utilizar a plataforma.
        </Text>
      </Box>

      <Box className="mt-4 w-full items-center justify-center">
        <Box className="shadow-xl">
          <Button
            size="lg"
            onPress={() => {
              // Close the drawer first
              onClose?.();
              // Then navigate to the dashboard with a slight delay to ensure the drawer is closed
              setTimeout(() => {
                router.replace("/");
              }, 100);
            }}
          >
            <Text>Visualizar meu painel</Text>
            <ArrrowIcon className="-mr-1 ml-2 h-5 w-5 text-text-inverse" />
          </Button>
        </Box>
        {/* <Button className="mt-2" variant="link" onPress={onClose}>
          <Text>Fechar este painel</Text>
        </Button> */}
      </Box>
    </Box>
  );
};

export default TeamCreationSuccess;
