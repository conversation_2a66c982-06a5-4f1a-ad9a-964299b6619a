import React from "react";
import { View } from "react-native";
import Animated, { useAnimatedProps, useSharedValue, withDelay, withTiming } from "react-native-reanimated";
import { Circle, Svg } from "react-native-svg";
import { Text } from "../ui";
import { fillVariants, trackVariants } from "./style";

type CircularProgressBarProps = {
  progress: number;
  delay?: number;
  size?: number;
  strokeWidth?: number;
  label?: string;
  inverted?: boolean;
  variant?: "default" | "error";
};

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

export const CircularProgressBar = ({
  progress,
  delay = 100,
  size = 48,
  strokeWidth = 6,
  label,
  inverted = false,
  variant = "default",
}: CircularProgressBarProps) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const animatedProgress = useSharedValue(circumference);

  React.useEffect(() => {
    animatedProgress.value = withDelay(
      delay,
      withTiming(circumference * (inverted ? progress : 1 - progress), {
        duration: 800,
      }),
    );
  }, [progress, animatedProgress, delay, inverted, circumference]);

  const animatedProps = useAnimatedProps(() => ({
    strokeDashoffset: animatedProgress.value,
  }));

  return (
    <View className="items-center justify-center">
      <Svg width={size} height={size} style={{ transform: [{ rotate: "-90deg" }] }}>
        <Circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          className={trackVariants({ variant })}
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        <AnimatedCircle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={circumference}
          // @ts-ignore
          animatedProps={animatedProps}
          className={fillVariants({ variant })}
          strokeLinecap="round"
        />
      </Svg>
      {label && (
        <View className="absolute inset-0 items-center justify-center">
          <Text className="text-center font-bold text-primary text-xs">{label}</Text>
        </View>
      )}
    </View>
  );
};
