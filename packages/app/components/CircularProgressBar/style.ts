import { cva } from "@/utils";

export const fillVariants = cva({
  base: [],
  variants: {
    variant: {
      default: ["stroke-primary"],
      destructive: ["stroke-destructive"],
    },
  },
  defaultVariants: {
    variant: "default",
  },
});

export const trackVariants = cva({
  base: [],
  variants: {
    variant: {
      default: ["stroke-primary-100"],
      destructive: ["stroke-destructive-100"],
    },
  },
  defaultVariants: {
    variant: "default",
  },
});
