import React, { useEffect, useRef } from 'react';
import { Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  runOnJS,
  useAnimatedGestureHandler,
} from 'react-native-reanimated';
import { PanGestureHandler, PanGestureHandlerGestureEvent } from 'react-native-gesture-handler';
import { toast as toastHandler, Toast as ToastType, useToaster } from '@backpackapp-io/react-native-toast';
import { Box, Center, Text, Heading } from '@/components/ui';
import { cn } from '@/utils';
import { FeaturedIcon } from '../FeaturedIcon';
import CheckIcon from '@platform/assets/icons/solid/checkmark-circle-02.svg';
import ErrorIcon from '@platform/assets/icons/multiplication-sign-02.svg';
import WarningIcon from '@platform/assets/icons/alert-circle.svg';
import InfoIcon from '@platform/assets/icons/help-circle.svg';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const TOAST_WIDTH = 400;
const TOAST_MIN_HEIGHT = 60;
const TOAST_MARGIN = 16;
const SWIPE_THRESHOLD = TOAST_WIDTH * 0.3;

type ToasterHandlers = {
  startPause: () => void;
  endPause: () => void;
  updateHeight: (toastId: string, height: number) => void;
  calculateOffset: (toast: ToastType, opts?: { reverseOrder?: boolean; gutter?: number }) => number;
};

interface AnimatedToastProps {
  toast: ToastType;
  onDismiss: (id: string) => void;
  handlers: ToasterHandlers;
}

const AnimatedToast: React.FC<AnimatedToastProps> = ({ toast, onDismiss, handlers }) => {
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(30); // Start from below
  const opacity = useSharedValue(0);
  const scale = useSharedValue(0.95);
  const isDismissing = useRef(false);

  // Animate the toast's vertical position
  const bottomOffset = handlers.calculateOffset(toast, {
    reverseOrder: true,
    gutter: TOAST_MARGIN,
  });
  const positionY = useSharedValue(bottomOffset);

  useEffect(() => {
    positionY.value = withSpring(bottomOffset + TOAST_MARGIN, {
      damping: 25,
      stiffness: 400,
      mass: 0.8,
    });
  }, [bottomOffset]);

  // Animate enter/exit based on visibility
  useEffect(() => {
    if (toast.visible) {
      // Entrance animation
      translateY.value = withSpring(0, { damping: 25, stiffness: 400, mass: 0.8 });
      opacity.value = withTiming(1, { duration: 250 });
      scale.value = withSpring(1, { damping: 20, stiffness: 300, mass: 0.8 });
    } else {
      // Exit animation - slide up and fade out
      translateY.value = withTiming(-30, { duration: 200 });
      opacity.value = withTiming(0, { duration: 200 });
      scale.value = withTiming(0.95, { duration: 200 });
    }
  }, [toast.visible]);

  const handleDismiss = () => {
    if (isDismissing.current) return;
    isDismissing.current = true;
    onDismiss(toast.id);
  };

  const gestureHandler = useAnimatedGestureHandler<
    PanGestureHandlerGestureEvent,
    { startX: number }
  >({
    onStart: (_, context) => {
      context.startX = translateX.value;
    },
    onActive: (event, context) => {
      if (!toast.isSwipeable) return;

      // Only allow swiping to the right (positive direction)
      if (event.translationX > 0) {
        translateX.value = context.startX + event.translationX;

        // Reduce opacity as user swipes
        const progress = Math.min(event.translationX / SWIPE_THRESHOLD, 1);
        opacity.value = 1 - progress * 0.7;
        scale.value = 1 - progress * 0.05;
      }
    },
    onEnd: (event) => {
      if (!toast.isSwipeable) return;

      if (event.translationX > SWIPE_THRESHOLD) {
        // Dismiss the toast - slide to the right
        translateX.value = withTiming(TOAST_WIDTH, { duration: 200 });
        opacity.value = withTiming(0, { duration: 200 });
        runOnJS(handleDismiss)();
      } else {
        // Snap back to original position
        translateX.value = withSpring(0, {
          damping: 25,
          stiffness: 400,
          mass: 0.8,
        });
        opacity.value = withTiming(1, { duration: 200 });
        scale.value = withTiming(1, { duration: 200 });
      }
    },
  });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      position: 'absolute',
      bottom: positionY.value,
      right: TOAST_MARGIN,
      width: Math.min(TOAST_WIDTH, screenWidth - TOAST_MARGIN * 2),
      minHeight: TOAST_MIN_HEIGHT,
      zIndex: 1000 + (toast.position || 0),
      opacity: opacity.value,
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: scale.value },
      ],
    };
  });

  const getToastVariant = (type: string) => {
    switch (type.toLowerCase()) {
      case 'success':
        return 'bg-background shadow-[0_4px_18px_0_rgba(var(--color-success-500),0.1)] border-success-950/10 text-success-800 dark:bg-success-900/20 dark:border-success-800 dark:text-success-200';
      case 'error':
        return 'bg-background-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200';
      case 'warning':
        return 'bg-background-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-200';
      case 'info':
        return 'bg-background-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-200';
      default:
        return 'bg-background border-border text-foreground shadow-md';
    }
  };

  const getToastIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'success':
        return CheckIcon;
      case 'error':
        return ErrorIcon;
      case 'warning':
        return WarningIcon;
      case 'info':
        return InfoIcon;
      default:
        return CheckIcon;
    }
  };

  const getToastIconVariant = (type: string) => {
    switch (type.toLowerCase()) {
      case 'success':
        return 'text-success-600 dark:text-success-400';
      case 'error':
        return 'text-red-600 dark:text-red-400';
      case 'warning':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'info':
        return 'text-blue-600 dark:text-blue-400';
      default:
        return 'text-foreground';
    }
  };

  const parseToastMessage = (message: string): { title: string; description?: string } => {
    const match = message.match(/^(.+?)\[(.+?)\]$/);

    if (match) {
      return {
        title: match[1].trim(),
        description: match[2].trim()
      };
    }

    return {
      title: message.trim(),
      description: undefined
    };
  };

  return (
    <PanGestureHandler onGestureEvent={gestureHandler} enabled={toast.isSwipeable}>
      <Animated.View
        style={animatedStyle}
        onLayout={(event) => {
          const { height } = event.nativeEvent.layout;
          handlers.updateHeight(toast.id, height);
        }}
      >
        <Box
          className={cn(
            'flex-row items-start justify-start rounded-xl border p-6',
            getToastVariant(toast.type)
          )}
        >
          <Center className="mr-4 rounded-full bg-background">
            {React.createElement(getToastIcon(toast.type), {
              className: `h-6 w-6 ${getToastIconVariant(toast.type)}`
            })}
          </Center>
          <Box className="flex-1 mr-2">
            {(() => {
              const messageText = typeof toast.message === 'function' ? toast.message(toast) : toast.message;

              // If the message is not a string (e.g., React element), render it directly
              if (typeof messageText !== 'string') {
                return (
                  <Box>
                    {messageText}
                  </Box>
                );
              }

              const { title, description } = parseToastMessage(messageText);

              return (
                <>
                  <Heading size="md" className="font-medium leading-6" numberOfLines={2}>
                    {title}
                  </Heading>
                  {description && (
                    <Text className="text-sm text-text-tertiary leading-4 mt-1 opacity-80" numberOfLines={2}>
                      {description}
                    </Text>
                  )}
                </>
              );
            })()}
          </Box>

          {/* {toast.isSwipeable && (
            <Box className="opacity-50 ml-2">
              <Text className="text-xs text-muted-foreground">→</Text>
            </Box>
          )} */}
        </Box>
      </Animated.View>
    </PanGestureHandler>
  );
};

export const Toast: React.FC = () => {
  const { toasts, handlers } = useToaster();

  const handleDismiss = (id: string) => {
    toastHandler.dismiss(id);
  };

  return (
    <>
      {toasts.map((toast) => (
        <AnimatedToast
          key={toast.id}
          toast={toast}
          onDismiss={handleDismiss}
          handlers={handlers as ToasterHandlers}
        />
      ))}
    </>
  );
};

export default Toast;
