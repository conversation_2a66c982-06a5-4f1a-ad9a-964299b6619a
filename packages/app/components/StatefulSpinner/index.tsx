import React, { useEffect } from "react";
import { View } from "react-native";
import Animated, {
  Easing,
  useAnimatedProps,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withRepeat,
  withSequence,
  withTiming,
  withSpring,
} from "react-native-reanimated";
import SuccessIcon from "@/assets/icons/tick-02.svg";
import ErrorIcon from "@/assets/icons/multiplication-sign-02.svg";
import { Circle, Svg } from "react-native-svg";
import { Text } from "../ui";


const AnimatedCircle = Animated.createAnimatedComponent(Circle);

type StatefulSpinnerProps = {
  size?: number;
  strokeWidth?: number;
  label?: string;
  state: "loading" | "success" | "destructive" | "idle";
};

const timings = {
  colorTransition: 300,
  progressDuration: 600,
  resetProgressDuration: 150,
  resetRotationDuration: 300,
};

export const StatefulSpinner = ({ size = 24, strokeWidth: strokeWidthProp, label, state }: StatefulSpinnerProps) => {
  const strokeWidth = strokeWidthProp || size >= 48 ? size * 0.1 : size * 0.15;
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const iconSize = size >= 48 ? size * 0.4 : size * 0.65;

  const animatedProgress = useSharedValue(circumference * 2);
  const rotation = useSharedValue(0);

  // Animation shared values for Success and Error icons
  const successOpacity = useSharedValue(0);
  const successScale = useSharedValue(0.4);
  const successRotation = useSharedValue(-45);

  const errorOpacity = useSharedValue(0);
  const errorScale = useSharedValue(0.4);
  const errorRotation = useSharedValue(-45);

  useEffect(() => {
    switch (state) {
      case "loading":
        handleLoadingAnimation();
        break;
      case "success":
        handleSuccessAnimation();
        break;
      case "destructive":
        handleErrorAnimation();
        break;
      default:
        handleIdle();
        break;
    }

    return undefined;
  }, [state]);

  const handleIdle = () => {
    rotation.value = withTiming(0, { duration: timings.resetRotationDuration, easing: Easing.linear });
    animatedProgress.value = withTiming(circumference * 2, {
      duration: timings.resetProgressDuration,
      easing: Easing.linear,
    });

    animatedProgress.value = withSequence(
      withTiming(circumference * 2, {
        duration: timings.progressDuration,
        easing: Easing.linear,
      }),
    );

    // Hide both icons
    successOpacity.value = withTiming(0, { duration: 200 });
    successScale.value = withTiming(0.4, { duration: 200 });
    successRotation.value = withTiming(-45, { duration: 200 });

    errorOpacity.value = withTiming(0, { duration: 200 });
    errorScale.value = withTiming(0.4, { duration: 200 });
    errorRotation.value = withTiming(-45, { duration: 200 });
  };

  const handleLoadingAnimation = () => {
    rotation.value = 0;
    animatedProgress.value = withTiming(circumference * 2, {
      duration: timings.resetProgressDuration,
      easing: Easing.linear,
    });

    // Reset both icon animations
    successOpacity.value = withTiming(0, { duration: 200 });
    successScale.value = withTiming(0.4, { duration: 200 });
    successRotation.value = withTiming(-45, { duration: 200 });

    errorOpacity.value = withTiming(0, { duration: 200 });
    errorScale.value = withTiming(0.4, { duration: 200 });
    errorRotation.value = withTiming(-45, { duration: 200 });

    animatedProgress.value = withRepeat(
      withSequence(
        withTiming(circumference * (1 - 0.8), {
          duration: timings.progressDuration * 4,
          easing: Easing.linear,
        }),
        withTiming(circumference * (1 - 0.1), {
          duration: timings.progressDuration * 4,
          easing: Easing.linear,
        }),
      ),
      -1,
      true,
    );

    rotation.value = withRepeat(withTiming(360, { duration: 1000, easing: Easing.linear }), -1, false);
  };

  const handleSuccessAnimation = () => {
    rotation.value = withTiming(0, { duration: timings.resetRotationDuration, easing: Easing.linear });
    animatedProgress.value = withTiming(circumference * 2, {
      duration: timings.resetProgressDuration,
      easing: Easing.linear,
    });



    animatedProgress.value = withSequence(
      withTiming(circumference * 2, {
        duration: timings.progressDuration,
        easing: Easing.linear,
      }),
    );

    // Reset error icon animation values
    errorOpacity.value = withTiming(0, { duration: 200 });
    errorScale.value = withTiming(0.4, { duration: 200 });
    errorRotation.value = withTiming(-45, { duration: 200 });

    // Animate success icon with 500ms delay
    successOpacity.value = withDelay(500, withTiming(1, { duration: 300 }));
    successScale.value = withDelay(500, withSpring(1, { damping: 10, stiffness: 132 }));
    successRotation.value = withDelay(500, withSpring(0, { damping: 12, stiffness: 120 }));
  };

  const handleErrorAnimation = () => {
    rotation.value = withTiming(0, { duration: timings.resetRotationDuration, easing: Easing.linear });
    animatedProgress.value = withTiming(circumference * 2, {
      duration: timings.resetProgressDuration,
      easing: Easing.linear,
    });



    animatedProgress.value = withSequence(
      withTiming(circumference * 2, {
        duration: timings.progressDuration,
        easing: Easing.linear,
      }),
    );

    // Reset success icon animation values
    successOpacity.value = withTiming(0, { duration: 200 });
    successScale.value = withTiming(0.4, { duration: 200 });
    successRotation.value = withTiming(-45, { duration: 200 });

    // Animate error icon with 500ms delay
    errorOpacity.value = withDelay(500, withTiming(1, { duration: 300 }));
    errorScale.value = withDelay(500, withSpring(1, { damping: 10, stiffness: 132 }));
    errorRotation.value = withDelay(500, withSpring(0, { damping: 12, stiffness: 120 }));
  };

  const animatedContainerStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${rotation.value}deg` }],
  }));

  // Animation styles for success and error icons
  const successIconStyle = useAnimatedStyle(() => ({
    opacity: successOpacity.value,
    transform: [{ scale: successScale.value }, { rotate: `${successRotation.value}deg` }],
  }));

  const errorIconStyle = useAnimatedStyle(() => ({
    opacity: errorOpacity.value,
    transform: [{ scale: errorScale.value }, { rotate: `${errorRotation.value}deg` }],
  }));

  const animatedCircleStyle = useAnimatedProps(() => ({
    strokeDashoffset: animatedProgress.value,
  }));

  return (
    <View className="items-center justify-center">
      <Animated.View style={[{ position: "absolute", zIndex: 10 }, successIconStyle]}>
        <SuccessIcon className="stroke-[4px] text-success" width={iconSize} height={iconSize} />
      </Animated.View>
      <Animated.View style={[{ position: "absolute", zIndex: 10 }, errorIconStyle]}>
        <ErrorIcon className="stroke-[4px] text-destructive" width={iconSize} height={iconSize} />
      </Animated.View>
      <Animated.View style={animatedContainerStyle}>
        <Svg width={size} height={size}>
          <Circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            strokeWidth={strokeWidth}
            fill="transparent"
            className="stroke-primary-100"
          />
          <AnimatedCircle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={circumference}
            animatedProps={animatedCircleStyle}
            strokeLinecap="round"
            rotation={-90}
            origin={`${size / 2}, ${size / 2}`}
            className={state === "success" ? "stroke-success" : state === "destructive" ? "stroke-destructive" : "stroke-primary"}
          />
        </Svg>
      </Animated.View>
      {label && (
        <View className="absolute inset-0 items-center justify-center">
          <Text className="text-center font-bold text-primary text-sm">{label}</Text>
        </View>
      )}
    </View>
  );
};

export default StatefulSpinner;
