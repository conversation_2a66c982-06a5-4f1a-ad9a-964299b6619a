// This project uses code from shadcn/ui.
// The code is licensed under the MIT License.
// https://github.com/shadcn/ui

import * as React from "react";
import {
  Controller,
  type ControllerProps,
  type FieldPath,
  type FieldValues,
  FormProvider,
  type Noop,
  useFormContext,
} from "react-hook-form";
import { View } from "react-native";
import type Animated from "react-native-reanimated";
import { FadeInDown, FadeOut } from "react-native-reanimated";
import { Checkbox } from "../checkbox";
import { Input } from "../input";
import { Label } from "../label";
import { RadioGroup } from "../radio-group";
import {
  Select,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectIcon,
  SelectInput,
  SelectItem,
  SelectPortal,
  SelectTrigger,
  type Option,
} from "../select";
import { Switch } from "../switch";
import { Textarea } from "../textarea";
import { Text } from "../text";
import Reanimated from "react-native-reanimated";
import { cn, cx } from "@/utils";
import { Box } from "./box";

const Form = FormProvider;

type FormFieldContextValue<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = {
  name: TName;
};

const FormFieldContext = React.createContext<FormFieldContextValue>({} as FormFieldContextValue);

const FormField = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  ...props
}: ControllerProps<TFieldValues, TName>) => {
  return (
    <FormFieldContext.Provider value={{ name: props.name }}>
      <Controller {...props} />
    </FormFieldContext.Provider>
  );
};

const useFormField = () => {
  const fieldContext = React.useContext(FormFieldContext);
  const itemContext = React.useContext(FormItemContext);
  const { getFieldState, formState, handleSubmit } = useFormContext();

  const fieldState = getFieldState(fieldContext.name, formState);

  if (!fieldContext) {
    throw new Error("useFormField should be used within <FormField>");
  }

  const { nativeID } = itemContext;

  return {
    nativeID,
    name: fieldContext.name,
    formItemNativeID: `${fieldContext.name}-form-item`,
    formDescriptionNativeID: `${fieldContext.name}-form-item-description`,
    formMessageNativeID: `${fieldContext.name}-form-item-message`,
    handleSubmit,
    ...fieldState,
  };
};

type FormItemContextValue = {
  nativeID: string;
};

const FormItemContext = React.createContext<FormItemContextValue>({} as FormItemContextValue);

const FormItem = React.forwardRef<React.ElementRef<typeof View>, React.ComponentPropsWithoutRef<typeof View>>(
  ({ className, ...props }, ref) => {
    const nativeID = React.useId();

    return (
      <FormItemContext.Provider value={{ nativeID }}>
        <View ref={ref} className={cn("w-full gap-2", className)} {...props} />
      </FormItemContext.Provider>
    );
  },
);
FormItem.displayName = "FormItem";

const FormLabel = React.forwardRef<
  React.ElementRef<typeof Label>,
  Omit<React.ComponentPropsWithoutRef<typeof Label>, "children"> & {
    children: string;
  }
>(({ className, nativeID: _nativeID, ...props }, ref) => {
  const { error, formItemNativeID } = useFormField();

  return (
    <Label ref={ref} className={cn("", error && "text-destructive-700", className)} nativeID={formItemNativeID} {...props} />
  );
});
FormLabel.displayName = "FormLabel";

const FormDescription = React.forwardRef<React.ElementRef<typeof Text>, React.ComponentPropsWithoutRef<typeof Text>>(
  ({ className, ...props }, ref) => {
    const { formDescriptionNativeID } = useFormField();

    return (
      <Text
        ref={ref}
        nativeID={formDescriptionNativeID}
        className={cn("pt-1 font-medium text-text-tertiary text-xs", className)}
        {...props}
      />
    );
  },
);
FormDescription.displayName = "FormDescription";

const FormMessage = React.forwardRef<
  React.ElementRef<typeof Animated.Text>,
  Omit<React.ComponentPropsWithoutRef<typeof Animated.Text>, "tabIndex"> & {
    tabIndex?: 0 | -1;
  }
>(({ className, children, ...props }, ref) => {
  const { error } = useFormField();
  const body = error ? String(error?.message) : children;

  if (!body) {
    return null;
  }

  return (
    <Reanimated.View entering={FadeInDown} exiting={FadeOut.duration(275)}>
      <Text
        className={cn("mb-4 rounded border border-destructive-100 bg-destructive-50 p-2 text-destructive text-xs", className)}
        {...props}
        ref={ref}
      >
        {body as React.ReactNode}
      </Text>
    </Reanimated.View>
  );
});
FormMessage.displayName = "FormMessage";

type Override<T, U> = Omit<T, keyof U> & U;

interface FormFieldFieldProps<T> {
  name: string;
  onBlur: Noop;
  onChange: (val: T) => void;
  value: T;
  disabled?: boolean;
}

type FormItemProps<T extends React.ElementType<any>, U> = Override<
  React.ComponentPropsWithoutRef<T>,
  FormFieldFieldProps<U>
> & {
  label?: string;
  description?: string;
};

const FormInput = React.forwardRef<React.ElementRef<typeof Input>, FormItemProps<typeof Input, string>>(
  ({ label, description, onChange, autoFocus, ...props }, ref) => {
    const inputRef = React.useRef<React.ComponentRef<typeof Input> | null>(null);
    const { error, formItemNativeID, formDescriptionNativeID, formMessageNativeID } = useFormField();

    // Apply autoFocus when the component mounts
    React.useEffect(() => {
      if (autoFocus && inputRef.current) {
        // Small delay to ensure the component is fully rendered
        const timer = setTimeout(() => {
          inputRef.current?.focus();
        }, 100);
        return () => clearTimeout(timer);
      }
    }, [autoFocus]);

    React.useImperativeHandle(
      ref,
      () => {
        if (inputRef.current === null) {
          throw new Error("Input ref is null");
        }
        return inputRef.current;
      },
      [],
    );

    function handleOnLabelPress() {
      if (!inputRef.current) return;

      if (inputRef.current.isFocused()) {
        inputRef.current.blur();
      } else {
        inputRef.current.focus();
      }
    }

    return (
      <FormItem>
        {!!label && (
          <FormLabel nativeID={formItemNativeID} onPress={handleOnLabelPress}>
            {label}
          </FormLabel>
        )}

        <Input
          ref={inputRef}
          aria-labelledby={formItemNativeID}
          aria-describedby={!error ? `${formDescriptionNativeID}` : `${formDescriptionNativeID} ${formMessageNativeID}`}
          aria-invalid={!!error}
          onChangeText={(masked, unmasked, obfuscated) => {
            onChange?.(unmasked);
          }}
          {...props}
        />
        {!!description && <FormDescription>{description}</FormDescription>}
        <FormMessage />
      </FormItem>
    );
  },
);

FormInput.displayName = "FormInput";

const FormTextarea = React.forwardRef<React.ElementRef<typeof Textarea>, FormItemProps<typeof Textarea, string>>(
  ({ label, description, onChange, ...props }, ref) => {
    const { error, formItemNativeID, formDescriptionNativeID, formMessageNativeID } = useFormField();

    function handleOnLabelPress() {
      if (!ref || typeof ref === "function") return;

      const textareaRef = ref.current;
      if (!textareaRef) return;

      if (textareaRef.isFocused()) {
        textareaRef.blur();
      } else {
        textareaRef.focus();
      }
    }

    return (
      <FormItem>
        {!!label && (
          <FormLabel nativeID={formItemNativeID} onPress={handleOnLabelPress}>
            {label}
          </FormLabel>
        )}

        <Textarea
          ref={ref}
          aria-labelledby={formItemNativeID}
          aria-describedby={!error ? `${formDescriptionNativeID}` : `${formDescriptionNativeID} ${formMessageNativeID}`}
          aria-invalid={!!error}
          onChangeText={onChange}
          {...props}
        />
        {!!description && <FormDescription>{description}</FormDescription>}
        <FormMessage />
      </FormItem>
    );
  },
);

FormTextarea.displayName = "FormTextarea";

const FormCheckbox = React.forwardRef<
  React.ElementRef<typeof Checkbox>,
  Omit<FormItemProps<typeof Checkbox, boolean>, "checked" | "onCheckedChange">
>(({ label, description, value, onChange, ...props }, ref) => {
  const { error, formItemNativeID, formDescriptionNativeID, formMessageNativeID } = useFormField();

  function handleOnLabelPress() {
    onChange?.(!value);
  }

  return (
    <FormItem className="px-1">
      <View className="flex-row items-center gap-3">
        <Checkbox
          ref={ref}
          aria-labelledby={formItemNativeID}
          aria-describedby={!error ? `${formDescriptionNativeID}` : `${formDescriptionNativeID} ${formMessageNativeID}`}
          aria-invalid={!!error}
          onCheckedChange={onChange}
          checked={value}
          {...props}
        />
        {!!label && (
          <Box className="flex-1">
            <FormLabel className="pb-0" nativeID={formItemNativeID} onPress={handleOnLabelPress}>
              {label}
            </FormLabel>
          </Box>
        )}
      </View>
      {!!description && <FormDescription>{description}</FormDescription>}
      <FormMessage />
    </FormItem>
  );
});

FormCheckbox.displayName = "FormCheckbox";

const FormRadioGroup = React.forwardRef<
  React.ElementRef<typeof RadioGroup>,
  Omit<FormItemProps<typeof RadioGroup, string>, "onValueChange">
>(({ label, description, value, onChange, ...props }, ref) => {
  const { error, formItemNativeID, formDescriptionNativeID, formMessageNativeID } = useFormField();

  return (
    <FormItem className="gap-3">
      <View>
        {!!label && <FormLabel nativeID={formItemNativeID}>{label}</FormLabel>}
        {!!description && <FormDescription className="pt-0">{description}</FormDescription>}
      </View>
      <RadioGroup
        ref={ref}
        aria-labelledby={formItemNativeID}
        aria-describedby={!error ? `${formDescriptionNativeID}` : `${formDescriptionNativeID} ${formMessageNativeID}`}
        aria-invalid={!!error}
        onValueChange={onChange}
        value={value}
        {...props}
      />

      <FormMessage />
    </FormItem>
  );
});

FormRadioGroup.displayName = "FormRadioGroup";

const FormSelect = React.forwardRef<
  React.ElementRef<typeof Select>,
  Omit<FormItemProps<typeof Select, Partial<Option>>, "open" | "onOpenChange" | "onValueChange">
>(({ label, description, placeholder, onChange, value, children, ...props }, ref) => {
  const { error, formItemNativeID, formDescriptionNativeID, formMessageNativeID } = useFormField();

  const selectedLabel = React.useMemo(() => {
    if (!value) return "";

    let label = "";
    React.Children.forEach(children, (child) => {
      if (React.isValidElement(child) && child.props.value === value) {
        label = child.props.label;
      }
    });

    return label;
  }, [children, value]);

  return (
    <FormItem>
      {!!label && <FormLabel nativeID={formItemNativeID}>{label}</FormLabel>}
      <Select
        ref={ref}
        aria-labelledby={formItemNativeID}
        aria-describedby={!error ? `${formDescriptionNativeID}` : `${formDescriptionNativeID} ${formMessageNativeID}`}
        aria-invalid={!!error}
        onValueChange={onChange}
        selectedValue={value || null}
        {...props}
      >
        <SelectTrigger>
          <SelectInput value={selectedLabel} placeholder={placeholder || "Selecione uma opção"} />
          <SelectIcon size="xl" className="text-text-tertiary" />
        </SelectTrigger>
        <SelectPortal>
          <SelectBackdrop />
          <SelectContent>
            <SelectDragIndicatorWrapper>
              <SelectDragIndicator />
            </SelectDragIndicatorWrapper>
            {children}
          </SelectContent>
        </SelectPortal>
      </Select>
      {!!description && <FormDescription>{description}</FormDescription>}
      <FormMessage />
    </FormItem>
  );
});

FormSelect.displayName = "FormSelect";

const FormSwitch = React.forwardRef<
  React.ElementRef<typeof Switch>,
  Omit<FormItemProps<typeof Switch, boolean>, "checked" | "onCheckedChange">
>(({ label, description, value, onChange, ...props }, ref) => {
  const { error, formItemNativeID, formDescriptionNativeID, formMessageNativeID } = useFormField();

  function handleOnLabelPress() {
    onChange?.(!value);
  }

  return (
    <FormItem className="px-1">
      <View className="flex-row items-center gap-3">
        <Switch
          ref={ref}
          aria-labelledby={formItemNativeID}
          aria-describedby={!error ? `${formDescriptionNativeID}` : `${formDescriptionNativeID} ${formMessageNativeID}`}
          aria-invalid={!!error}
          onCheckedChange={onChange}
          checked={value}
          {...props}
        />
        {!!label && (
          <FormLabel
            className={cx("text-text", { "text-text-secondary": !value })}
            nativeID={formItemNativeID}
            onPress={handleOnLabelPress}
          >
            {label}
          </FormLabel>
        )}
      </View>
      {!!description && <FormDescription>{description}</FormDescription>}
      <FormMessage />
    </FormItem>
  );
});

FormSwitch.displayName = "FormSwitch";

export {
  Form,
  FormCheckbox,
  FormDescription,
  FormField,
  FormInput,
  FormItem,
  FormLabel,
  FormMessage,
  FormRadioGroup,
  FormSelect,
  FormSwitch,
  FormTextarea,
  useFormField,
};
