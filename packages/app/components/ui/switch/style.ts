import { cva } from "@/utils";

export const switchVariants = cva({
  base: [
    "peer",
    "shrink-0",
    "cursor-pointer",
    "flex-row",
    "items-center",
    "rounded-full",
    "transition-colors",
    "focus-visible:outline-none",
    "focus-visible:ring-2",
    "focus-visible:ring-ring",
    "focus-visible:ring-offset-2",
    "focus-visible:ring-offset-background",
    "disabled:cursor-not-allowed",
  ],
  variants: {
    variant: {
      default: [],
      "outline-white": ["border", "border-background/80"],
      status: [],
    },
    checked: {
      true: [],
      false: [],
    },
    disabled: {
      true: ["opacity-40", "cursor-not-allowed", "pointer-events-none"],
      false: [],
    },
    size: {
      xs: ["h-4", "w-6", "p-1", "shadow-xs"],
      sm: ["h-5", "w-8", "p-1.5", "shadow-xs"],
      md: ["h-6", "w-9", "p-1.5", "shadow-sm"],
      lg: ["h-7", "w-11", "p-2", "shadow-sm"],
    },
  },
  compoundVariants: [
    {
      variant: "default",
      checked: true,
      class: "bg-primary",
    },
    {
      variant: "default",
      checked: false,
      class: "bg-gray-300",
    },
    {
      variant: "outline-white",
      checked: true,
      class: "bg-background/100 opacity-100",
    },
    {
      variant: "outline-white",
      checked: false,
      class: "bg-transparent opacity-60",
    },
    {
      variant: "status",
      checked: true,
      class: "bg-success",
    },
    {
      variant: "status",
      checked: false,
      class: "bg-destructive",
    },
  ],
  defaultVariants: {
    variant: "default",
    size: "md",
    disabled: false,
  },
});

export const switchThumbVariants = cva({
  base: ["pointer-events-none", "block", "rounded-full", "bg-background", "ring-0", "transition-transform"],
  variants: {
    size: {
      xs: ["h-2.5", "w-2.5", "shadow-[0_2px_4px_rgba(0,0,0,.1)]"],
      sm: ["h-3.5", "w-3.5", "shadow-sm"],
      md: ["h-4", "w-4", "shadow-[0_2px_4px_rgba(var(--color-border-darkest),0.4)]"],
      lg: ["h-5", "w-5", "shadow-md"],
    },
    checked: {
      true: [],
      false: [],
    },
  },
  compoundVariants: [
    {
      size: "xs",
      checked: true,
      class: "translate-x-1.5",
    },
    {
      size: "xs",
      checked: false,
      class: "-translate-x-[2px]",
    },
    {
      size: "sm",
      checked: true,
      class: "translate-x-2",
    },
    {
      size: "sm",
      checked: false,
      class: "-translate-x-1",
    },
    {
      size: "md",
      checked: true,
      class: "translate-x-2",
    },
    {
      size: "md",
      checked: false,
      class: "-translate-x-0.5",
    },
    {
      size: "lg",
      checked: true,
      class: "translate-x-2.5",
    },
    {
      size: "lg",
      checked: false,
      class: "-translate-x-1",
    },
  ],
  defaultVariants: {
    size: "md",
    checked: false,
  },
});

// Native-specific styles
export const nativeSwitchContainerVariants = cva({
  base: ["rounded-full"],
  variants: {
    disabled: {
      true: ["opacity-50"],
      false: [],
    },
  },
  defaultVariants: {
    disabled: false,
  },
});

export const nativeSwitchThumbVariants = cva({
  base: ["rounded-full", "bg-background", "shadow-foreground/25", "shadow-md", "ring-0"],
  variants: {
    size: {
      sm: ["h-6", "w-6"],
      md: ["h-7", "w-7"],
      lg: ["h-8", "w-8"],
    },
  },
  defaultVariants: {
    size: "md",
  },
});
