"use client";

import { Popover, PopoverBackdrop, <PERSON>over<PERSON>ontent, PopoverBody, PopoverArrow } from "@/components/ui/popover";
import * as React from "react";
import { forwardRef, useMemo, useState } from "react";
import { HexColorPicker } from "react-colorful";
import { Pressable } from "react-native";
import { Box } from "./custom/box";
import { Input } from "./input";
import { Text } from "./text";

interface ColorPickerProps {
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  placeholder?: string;
  label?: string;
}

const ColorPicker = forwardRef<React.ElementRef<typeof Input>, ColorPickerProps>(
  ({ value, onChange, onBlur, placeholder, label }, ref) => {
    const [open, setOpen] = useState(false);
    const [inputValue, setInputValue] = useState(value);
    const inputRef = React.useRef<React.ElementRef<typeof Input>>(null);
    const isRefocusingRef = React.useRef(false);

    React.useEffect(() => {
      setInputValue(value);
    }, [value]);

    React.useImperativeHandle(
      ref,
      () => {
        if (inputRef.current === null) {
          throw new Error("Input ref is null");
        }
        return inputRef.current;
      },
      [],
    );

    const handleInputChange = (text: string) => {
      let newText = text;
      if (!newText.startsWith("#")) {
        newText = `#${newText.toUpperCase().replace(/[^0-9A-F]/g, "")}`;
      } else {
        newText = `#${newText.substring(1).toUpperCase().replace(/[^0-9A-F]/g, "")}`;
      }

      if (newText.length > 7) {
        newText = newText.substring(0, 7);
      }

      setInputValue(newText);

      if (/^#([0-9A-F]{6})$/i.test(newText) || /^#([0-9A-F]{3})$/i.test(newText)) {
        onChange(newText);
      }
    };

    const colorForPicker = useMemo(() => {
      if (/^#([0-9A-F]{6})$/i.test(inputValue) || /^#([0-9A-F]{3})$/i.test(inputValue)) {
        return inputValue;
      }
      if (/^#([0-9A-F]{6})$/i.test(value) || /^#([0-9A-F]{3})$/i.test(value)) {
        return value;
      }
      return "#FFFFFF";
    }, [inputValue, value]);

    const colorPalette = useMemo(
      () => [
        // Primary colors
        "#f44336",
        "#e91e63",
        "#9c27b0",
        "#673ab7",
        "#3f51b5",
        "#2196f3",
        "#03a9f4",
        "#00bcd4",
        "#009688",
        "#4caf50",
        // Secondary colors
        "#8bc34a",
        "#cddc39",
        "#ffeb3b",
        "#ffc107",
        "#ff9800",
        "#ff5722",
        "#795548",
        "#9e9e9e",
        "#607d8b",
        // Grayscale
        "#000000",
        "#444444",
        "#888888",
        "#cccccc",
        "#ffffff",
      ],
      [],
    );

    return (
      <Box>
        {label && <Text className="mb-2 font-medium">{label}</Text>}
        <Box className="flex-row items-center bg-background-dark p-4 rounded-xl">
          <Popover
            placement="top"
            isOpen={open}
            onClose={() => setOpen(false)}
            onOpen={() => setOpen(true)}
            offset={5}
            trigger={(triggerProps) => (
              <Pressable
                {...triggerProps}
                onPress={() => setOpen(!open)}
                className="h-10 w-10 overflow-hidden rounded-md border border-border border-r-0 rounded-r-none"
                style={{ backgroundColor: colorForPicker }}
              />
            )}
          >
            <PopoverBackdrop />
            <PopoverContent className="w-72 p-0">
              <PopoverArrow />
              <PopoverBody>
                <Box className="p-3">
                  <Text className="mb-2 font-medium">Selecionar Cor</Text>

                  {/* HexColorPicker from react-colorful */}
                  <Box className="mb-4">
                    <HexColorPicker
                      color={colorForPicker}
                      onChange={(color) => {
                        setInputValue(color);
                        onChange(color);
                      }}
                      style={{ width: "100%", height: 170 }}
                    />
                  </Box>

                  {/* Color palette for quick selection */}
                  <Box className="flex-row flex-wrap gap-2 p-2">
                    {colorPalette.map((color) => (
                      <Pressable
                        key={color}
                        onPress={() => {
                          setInputValue(color);
                          onChange(color);
                          setOpen(false);
                        }}
                      >
                        <Box
                          style={{ backgroundColor: color }}
                          className="transition-transform duration-150 ease-in-out hover:scale-125 h-[1.465rem] w-[1.465rem] overflow-hidden rounded-full border border-border-light shadow-[inset_0_0_0_2px_rgba(var(--color-background),1)]"
                        />
                      </Pressable>
                    ))}
                  </Box>
                </Box>
              </PopoverBody>
            </PopoverContent>
          </Popover>
          <Input
            ref={inputRef}
            value={inputValue}
            onChangeText={handleInputChange}
            placeholder={placeholder || "#000000"}
            className="rounded-l-none"
            selectTextOnFocus={true}
            onFocus={() => {
              if (isRefocusingRef.current) {
                isRefocusingRef.current = false;
                return;
              }

              setOpen(true);

              // Select text after popover opens
              setTimeout(() => {
                if (inputRef.current) {
                  isRefocusingRef.current = true;
                  // @ts-ignore - blur and focus methods exist
                  inputRef.current.blur();
                  setTimeout(() => {
                    if (inputRef.current) {
                      // @ts-ignore - focus method exists
                      inputRef.current.focus();
                    }
                  }, 50);
                }
              }, 150);
            }}
            onBlur={() => {
              if (!(/^#([0-9A-F]{6})$/i.test(inputValue) || /^#([0-9A-F]{3})$/i.test(inputValue))) {
                setInputValue(value);
              }
            }}
          />
        </Box>
      </Box>
    );
  },
);

ColorPicker.displayName = "ColorPicker";

export { ColorPicker };
