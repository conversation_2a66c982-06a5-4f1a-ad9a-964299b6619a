import DashboardSquareIcon from "@/assets/icons/bulk/dashboard-square.svg";
import ChevronDownIcon from "@/assets/icons/chevron-down.svg";
import SearchIcon from "@/assets/icons/search.svg";
import { Box, Center, Heading, Input, Text } from "@/components/ui";
import { cx } from "@/utils";
import { LinearGradient } from "expo-linear-gradient";
import { type Href, Link, useSegments } from "expo-router";
// import ChevronRightIcon from "@/assets/icons/chevron-right.svg";

export const PageBreadcrumb = () => {
  const segments = useSegments();

  const breadcrumbItems = segments.map((segment, index) => {
    const isFirst = index === 0;
    const isLast = index === segments.length - 1;
    const href = `/${segments.slice(0, index + 1).join("/")}` as Href;
    const label = segment.charAt(0).toUpperCase() + segment.slice(1);

    return (
      <Box key={segment} className="h-fit flex-row items-center">
        {isFirst && (
          <Link href="/">
            <DashboardSquareIcon className="mr-2 h-3 w-3 text-text-secondary" />
          </Link>
        )}
        {!isFirst && (
          <Link href={href} asChild>
            <Text
              className={`${isLast ? "pointer-events-none font-medium text-text-tertiary text-xs" : "text-text text-xs hover:text-text-secondary_hover"}`}
            >
              {label}
            </Text>
          </Link>
        )}
        {!isLast && (
          <Text className="mx-1 text-text-tertiary">
            <ChevronDownIcon className="-rotate-90 h-3 w-3 text-text-quaternary" />
          </Text>
        )}
      </Box>
    );
  });

  return breadcrumbItems.length > 1 ? (
    <Box className="mb-6 w-fit flex-row items-center rounded">{breadcrumbItems}</Box>
  ) : null;
};

interface PageLayoutProps {
  children: React.ReactNode;
  raw?: boolean;
  pageTitle: string;
  pageIcon: React.FC<React.SVGProps<SVGSVGElement>>;
  description?: string;
  onSearch?: ((query: string) => void) | null;
  searchLabel?: string;
  actions?: React.ReactNode | React.ReactNode[];
  featuredSearchBar?: boolean;
}

export const PageRow = ({ children, className }: { children: React.ReactNode; className?: string }) => {
  return <Box className={cx("", className)}>{children}</Box>;
};

const PageLayout = ({
  children,
  pageTitle,
  pageIcon: PageIcon,
  description,
  onSearch,
  searchLabel,
  actions,
  featuredSearchBar,
  raw = false,
}: PageLayoutProps) => {
  return (
    <Box className="px-12 py-8">
      <PageBreadcrumb />
      <Box className="mb-12 flex-row items-center">
        <Center className="129, 142,0.4)] mr-4 overflow-hidden rounded-full shadow-[inset_0_6px_4px_0_rgba(123,">
          <LinearGradient
            colors={["rgba(123, 129, 142, 0)", "rgba(123, 129, 142,0.06)", "rgba(123, 129, 142, 0.16)"]}
            locations={[0, 0.6, 1]}
            start={{ x: 0, y: 1 }}
            end={{ x: 0, y: 0 }}
            className={cx("absolute h-full w-full", {})}
          />
          <Center className="m-2 h-12 w-12 overflow-hidden rounded-full border border-border-light bg-background shadow-sm">
            <PageIcon className="h-5 w-5 text-text-tertiary" />
          </Center>
        </Center>
        <Box>
          <Heading size="2xl" className="font-bold">
            {pageTitle}
          </Heading>
          {description && <Text className="mt-1 text-md text-text-tertiary">{description}</Text>}
        </Box>
        <Box className="flex-1 flex-row items-center">
          {onSearch && (
            <Box className={featuredSearchBar ? "flex-1" : ""}>
              <Input
                size="sm"
                placeholder={searchLabel || "Procurar..."}
                fullWidth={featuredSearchBar}
                beforeIcon={SearchIcon}
              />
            </Box>
          )}
        </Box>
        <Box className="flex-row items-center gap-4">
          {Array.isArray(actions) ? actions.map((action, index) => <Box key={index}>{action}</Box>) : actions}
        </Box>
      </Box>
      <Box className={cx({ "min-h-[75vh] rounded-xl border border-border-light bg-background shadow": !raw })}>
        {children}
      </Box>
    </Box>
  );
};

export default PageLayout;
