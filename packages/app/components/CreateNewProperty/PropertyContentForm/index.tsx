import ArrowRightIcon from "@/assets/icons/arrow-right.svg";
import PencilEditIcon from "@/assets/icons/bulk/pencil-edit.svg";
import DeleteIcon from "@/assets/icons/delete.svg";
import FileAddIcon from "@/assets/icons/file-add.svg";
import ImageIcon from "@/assets/icons/image.svg";
import PlaylistIcon from "@/assets/icons/play-list.svg";
import CheckmarkIcon from "@/assets/icons/tick.svg";
import UploadIcon from "@/assets/illustrations/undraw_photos_09tf.svg";
import { DrawerFooter } from "@/components/Drawer";
import DrawerHeader from "@/components/Drawer/DrawerHeader";
import { Box, Button, Center, FormField, FormInput, FormTextarea, LabelSeparator, Text } from "@/components/ui";
import { PROPERTY_TYPE_VALUE_LABEL_MAP } from "@/constants";
import { usePartialFormValidation } from "@/hooks";
import type { PropertyType } from "@/types";
import * as FileSystem from "expo-file-system";
import * as ImagePicker from "expo-image-picker";
import { useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import { Pressable } from "react-native";

const checkFileSize = async (fileURI: string) => {
  if (!fileURI) return;

  const fileSizeInBytes = await FileSystem.getInfoAsync(fileURI);
  return fileSizeInBytes;
};

const PropertyContentForm = ({
  onSubmit,
  onBack,
  onCancel,
}: {
  onSubmit?: () => void;
  onBack?: () => void;
  onCancel?: () => void;
}) => {
  const { control, resetField, setValue, watch, trigger } = useFormContext();

  const title = watch("title");
  const propertyType = watch("type", "apartment") as PropertyType;
  const builtArea = watch("built_area");
  const totalArea = watch("total_area");
  const neighborhood = watch("address.neighborhood");
  const mediaToUpload = watch("media") || [];

  const { isValid } = usePartialFormValidation({
    name: ["purpose", "type"],
    control,
  });

  const pickImage = async () => {
    // No permissions request is necessary for launching the image library
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ["images", "videos"],
      allowsEditing: false,
      allowsMultipleSelection: true,
      quality: 0.8,
    });

    if (!result.canceled) {
      const validAssets = [];
      for (const asset of result.assets) {
        const fileSize = asset.fileSize;

        if (fileSize && fileSize > 5 * 1024 * 1024) {
          alert("O tamanho do arquivo selecionado deve ser menor que 5MB");
          continue;
        }
        validAssets.push(asset);
      }

      if (validAssets.length > 0) {
        const currentMedia = watch("media") || [];
        setValue("media", [...currentMedia, ...validAssets]);
      }
    }
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <We need this to run only once>
  useEffect(() => {
    if (!title || title?.length === 0) {
      const propertyTypeLabel = PROPERTY_TYPE_VALUE_LABEL_MAP[propertyType];
      const areaLabel = builtArea || totalArea ? ` com ${builtArea}m2` : "";
      const neighborhoodLabel = neighborhood ? ` no bairro ${neighborhood}` : "";

      resetField("title", {
        defaultValue: `${propertyTypeLabel}${areaLabel}${neighborhoodLabel}.`,
      });
    }
  }, []);

  return (
    <Box className="pt-12">
      <DrawerHeader
        title="Conteúdo, fotos e vídeos"
        description="Adicione informações como título, descrição, fotos e vídeos."
        icon={PencilEditIcon}
      />

      <Box className="mb-8 gap-8">
        <LabelSeparator className="pl-4" label="Conteúdo" labelPosition="left" />
        <Box className="gap-6 px-8">
          <FormField
            control={control}
            name="title"
            render={({ field }) => (
              <FormInput
                description="Dica: Este será o título padrão utilizado na página do imóvel no seu site e portais de imóveis."
                label="Título"
                placeholder="Título do imóvel"
                {...field}
              />
            )}
          />
          <Box className="flex-1">
            <FormField
              control={control}
              name="description"
              render={({ field }) => (
                <FormTextarea
                  label="Descrição"
                  placeholder={"Digite uma descrição para o imóvel"}
                  description="Dica: Este sera o texto padrão utilizado na página do imóvel no seu site e portais de imóveis."
                  // isReadOnly
                  {...field}
                />
              )}
            />
          </Box>
        </Box>
        <LabelSeparator className="pl-4" label="Fotos e vídeos" labelPosition="left" />
        {mediaToUpload.length === 0 && (
          <Pressable onPress={pickImage}>
            <Box className="px-8">
              <Center className="rounded-xl border-2 border-border border-dashed bg-background-dark p-6">
                <Center className="mb-2 h-24 w-24">
                  <UploadIcon className="h-full w-full text-primary-400" />
                </Center>
                <Text className="mb-2 text-center font-medium text-text">Selecione fotos e vídeos do imóvel</Text>
                <Text className="mb-1 text-center text-sm text-text-secondary">
                  Dica: Adicione fotos e videos do imóvel para aumentar a visibilidade dele.
                </Text>
                <Text className="text-center font-medium text-sm text-text-tertiary">Tamanho máximo: 12mb</Text>
                <Button className="mt-4" onPress={pickImage} size="sm" variant="outline">
                  <FileAddIcon className="mr-2 h-4 w-4 text-text" />
                  <Text>Selecionar fotos e videos</Text>
                </Button>
              </Center>
            </Box>
          </Pressable>
        )}
        {mediaToUpload.length > 0 && (
          <Box className="px-8">
            <Box className="flex-wrap gap-2">
              {mediaToUpload.map((media: ImagePicker.ImagePickerAsset) => (
                <Center
                  key={media.fileName}
                  className="flex-row justify-start rounded-xl border border-border-lighter p-2 shadow-sm"
                >
                  <Center className="mr-4 h-8 w-8 rounded-lg border border-border bg-primary-25">
                    {media.type === "image" ? (
                      <ImageIcon className="h-5 w-5 text-primary" />
                    ) : (
                      <PlaylistIcon className="h-5 w-5 text-primary" />
                    )}
                  </Center>
                  <Box className="flex-1 flex-row">
                    <Text className="mr-4 font-medium text-sm text-text-secondary">{media.fileName}</Text>
                    <Text className="flex-1 text-text-tertiary text-xs">
                      {media.fileSize &&
                        (media.fileSize > 1024 * 1024
                          ? `${(media.fileSize / 1024 / 1024).toFixed(2)}mb`
                          : `${(media.fileSize / 1024).toFixed(2)}kb`)}
                    </Text>
                  </Box>
                  <Button
                    onPress={() => {
                      const currentMedia = watch("media") || [];
                      setValue(
                        "media",
                        currentMedia.filter((m: ImagePicker.ImagePickerAsset) => m.fileName !== media.fileName),
                      );
                    }}
                    size="sm"
                    variant="ghost"
                  >
                                        <DeleteIcon className="mr-2 h-5 w-5 text-destructive" />
                    <Text className="text-sm">Remover</Text>
                  </Button>
                </Center>
              ))}
            </Box>
          </Box>
        )}
        {mediaToUpload.length > 0 && (
          <Box className="px-8">
            <Button onPress={pickImage} size="sm" variant="outline">
              <FileAddIcon className="mr-2 h-5 w-5 text-text-inverse" />
              <Text>Selecionar mais fotos e videos</Text>
            </Button>
          </Box>
        )}
      </Box>

      <DrawerFooter
        primaryAction={{
          disabled: false,
          label: "Concluir e cadastrar imóvel",
          iconBefore: CheckmarkIcon,
          onPress: async () => {
            const output = await trigger(["title"], {
              shouldFocus: true,
            });

            if (!output) return;

            onSubmit?.();
          },
        }}
        secondaryAction={{
          label: "Voltar",
          // iconBefore: ArrowLeftIcon,
          onPress: async () => {
            onBack?.();
          },
        }}
        tertiaryAction={{
          label: "Cancelar",
          // iconBefore: ArrowLeftIcon,
          onPress: async () => {
            onCancel?.();
          },
        }}
      />
    </Box>
  );
};

export default PropertyContentForm;
