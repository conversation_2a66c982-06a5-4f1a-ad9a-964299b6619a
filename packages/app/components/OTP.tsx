import ForgotPasswordIcon from "@platform/assets/icons/forgot-password.svg";
import { useSupabaseAuth } from "@/hooks/useSupabaseAuth";
import { Box, Center, Heading } from "@/components/ui";
import { Button } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import { OtpInput as OriginalOtpInput, OtpInputProps, OtpInputRef } from "react-native-otp-entry";
import { cssInterop } from "nativewind";
import * as React from "react";
import Reanimated, { FadeIn, FadeOut, useAnimatedStyle, useSharedValue, withTiming } from "react-native-reanimated";
import { FeaturedIcon } from "./FeaturedIcon";
import AlertCircleIcon from "@platform/assets/icons/alert-circle.svg";

// Define the props that our wrapper will accept from cssInterop
interface OtpInputWrapperProps extends OtpInputProps {
  containerStyle?: Record<string, unknown>;
  pinCodeContainerStyle?: Record<string, unknown>;
  pinCodeTextStyle?: Record<string, unknown>;
  placeholderTextStyle?: Record<string, unknown>;
  focusedPinCodeContainerStyle?: Record<string, unknown>;
  focusStickStyle?: Record<string, unknown>;
}

const OtpInputWrapper = React.forwardRef<OtpInputRef, OtpInputWrapperProps>(
  ({ containerStyle, pinCodeContainerStyle, pinCodeTextStyle, focusedPinCodeContainerStyle, focusStickStyle, placeholderTextStyle, theme: originalTheme, ...props }, ref) => {
    const theme = React.useMemo(() => ({
      ...originalTheme,
      containerStyle,
      placeholderTextStyle,
      pinCodeContainerStyle,
      pinCodeTextStyle,
      focusedPinCodeContainerStyle,
      focusStickStyle,
    }), [originalTheme, containerStyle, placeholderTextStyle, pinCodeContainerStyle, pinCodeTextStyle, focusedPinCodeContainerStyle]);

    return <OriginalOtpInput {...props} theme={theme} ref={ref} />;
  }
);

const OtpInput = cssInterop(OtpInputWrapper, {
  containerClassName: "containerStyle",
  pinCodeContainerClassName: "pinCodeContainerStyle",
  placeholderClassName: "placeholderTextStyle",
  pinCodeTextClassName: "pinCodeTextStyle",
  focusPinCodeContainerClassName: "focusedPinCodeContainerStyle",
  focusStickClassName: "focusStickStyle",
});



// The main OTP component
interface OTPComponentProps {
  email: string;
  onVerified: () => void;
  onCancel: () => void;
}

export default function OTPComponent({
  email,
  onVerified,
  onCancel,
}: OTPComponentProps) {
  const { sendMagicLink, verifyOtp } = useSupabaseAuth();
  const otpInputRef = React.useRef<OtpInputRef>(null);
  const [otpValue, setOtpValue] = React.useState("");
  const [isVerifying, setIsVerifying] = React.useState(false);
  const [isResending, setIsResending] = React.useState(false);
  const [countdown, setCountdown] = React.useState(60);
  const [isInvalid, setIsInvalid] = React.useState(false);
  const progress = useSharedValue(0);
  const otpOpacity = useSharedValue(1);

  const animatedProgressStyle = useAnimatedStyle(() => {
    return {
      width: `${progress.value * 100}%`,
    };
  });

  React.useEffect(() => {
    const timer = setTimeout(() => {
      otpInputRef.current?.focus();
    }, 400); // Delay to ensure animation is complete and component is ready

    return () => clearTimeout(timer);
  }, []);

  React.useEffect(() => {
    if (isInvalid) {
      progress.value = withTiming(1, { duration: 1500 });
      otpOpacity.value = withTiming(0.5, { duration: 300 });

      const timer = setTimeout(() => {
        setIsInvalid(false);
        otpInputRef.current?.clear();
        progress.value = withTiming(0, { duration: 300 });
        otpOpacity.value = withTiming(1, { duration: 300 });
        setTimeout(() => {
          otpInputRef.current?.focus();
        }, 300);
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [isInvalid]);

  React.useEffect(() => {
    let timer: NodeJS.Timeout | undefined;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [countdown]);

  const handleResendCode = async () => {
    setIsResending(true);
    try {
      await sendMagicLink(email);
      setCountdown(60); // Start countdown when the email is sent
    } catch (error) {
      console.error("Failed to resend verification email:", error);
    } finally {
      setIsResending(false);
    }
  };

  async function verifyOtpCode(otp: string) {
    if (!email || otp.length !== 6) return;

    setIsVerifying(true);
    setIsInvalid(false);

    try {
      await verifyOtp(email, otp);
      onVerified();
    } catch (error) {
      console.error("OTP verification error:", error);
      setIsInvalid(true);
      otpInputRef.current?.clear();
    } finally {
      setIsVerifying(false);
    }
  }

  const handleOtpChange = (value: string) => {
    setOtpValue(value);
    setIsInvalid(false);

    // Automatically verify when all 6 digits are entered
    if (value.length === 6) {
      verifyOtpCode(value);
    }
  };

  return (
    <Reanimated.View
      entering={FadeIn.duration(300).delay(300)}
      exiting={FadeOut.duration(300)}
      style={{
        display: "flex",
        height: "100%",
        width: "100%",
        alignItems: "center",
        justifyContent: "center",
        position: "absolute",
      }}
    >
      <Center className="bg-background rounded-2xl border border-border-light p-8 max-w-[480px]">
        <Center className="mb-8">
          {/* <OTPIllustration className="h-64 w-64 text-primary" /> */}
          <FeaturedIcon variant="gradient" color="primary" size="xl" className="mb-6">
            <ForgotPasswordIcon className="h-8 w-8 text-[#FFF]" />
          </FeaturedIcon>
          <Heading size="xl" className="mb-4 font-medium text-gray-900">
            Código de segurança
          </Heading>
          <Text className="px-4 text-center text-text-secondary leading-5">
            Digite abaixo o <Text className="font-medium text-text-secondary">código de segurança de 6 digitos</Text> que enviamos para o email &nbsp;
            <Text className="font-medium text-text-secondary">{email}</Text>.
          </Text>
          <Button variant="ghost" onPress={onCancel} disabled={isVerifying} rounded="full">
            <Text className="font-bold">(Utilizar outro email)</Text>
          </Button>
        </Center>

        <Reanimated.View className="w-full">
          <Reanimated.View style={[{ opacity: otpOpacity }]} className="w-full mb-12" pointerEvents={isInvalid ? "none" : "auto"}>
            <OtpInput
              disabled={isVerifying || isResending || isInvalid}
              placeholder="••••••"
              ref={otpInputRef}
              numberOfDigits={6}
              onTextChange={handleOtpChange}
              onFilled={verifyOtpCode}
              containerClassName="w-full justify-center gap-3"
              pinCodeContainerClassName={`h-18 flex-1 items-center justify-center rounded-lg border bg-white ${isInvalid ? "border-destructive" : "border-gray-200"}`}
              pinCodeTextClassName="text-xl font-medium text-gray-900"
              placeholderClassName="text-text-tertiary opacity-50"
              focusPinCodeContainerClassName="shadow-[0_0_0_1px_rgba(var(--color-primary),1)] !border-primary"
              focusStickClassName="!bg-primary-900"
            />
            {isInvalid && (
              <Reanimated.View entering={FadeIn} exiting={FadeOut}>
                <Box className="w-full overflow-hidden flex-row items-center rounded border border-destructive-700/20 bg-destructive-50 p-2 mt-4">
                  <AlertCircleIcon className="h-5 w-5 text-destructive-600" />
                  <Text className="ml-3 text-destructive-900 text-sm">Código inválido</Text>
                  <Reanimated.View style={animatedProgressStyle} className="absolute bottom-0 left-0 h-0.5 bg-destructive-600" />
                </Box>
              </Reanimated.View>
            )}
          </Reanimated.View>
          <Box className="w-full mb-2">
            <Button className="w-full" onPress={() => verifyOtpCode(otpValue)} disabled={isVerifying || otpValue.length !== 6} rounded="full" size="lg">
              <Text>Verificar código</Text>
            </Button>
          </Box>
          <Button
            variant="link"
            rounded="full"
            size="lg"
            onPress={handleResendCode}
            isLoading={isResending || countdown > 0}
            loadingMessage={isResending ? "Reenviando código..." : undefined}
            disabled={isVerifying || countdown > 0}
          >
            <Text>{countdown > 0 ? `Reenviar código (Aguarde ${countdown}s)` : "Reenviar código"}</Text>
          </Button>
        </Reanimated.View>
      </Center>
    </Reanimated.View>
  );
}
