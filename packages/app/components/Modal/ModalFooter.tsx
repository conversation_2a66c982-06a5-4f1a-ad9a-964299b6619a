import { Portal } from "@gorhom/portal";
import { Box, Button, type ButtonVariants, Text } from "../ui"; // Added Button, ButtonVariants, Text

// Define Action Props type similar to DrawerFooterActionProps
export type ModalFooterActionProps = {
  disabled?: boolean;
  label: string;
  iconBefore?: React.FC<React.SVGProps<SVGSVGElement>>;
  iconAfter?: React.FC<React.SVGProps<SVGSVGElement>>;
  variant?: ButtonVariants["variant"];
  onPress: () => void;
  isLoading?: boolean;
  loadingMessage?: string;
};

// Update ModalFooterProps
export type ModalFooterProps = {
  children?: React.ReactNode; // Made children optional
  primaryAction?: ModalFooterActionProps;
  secondaryAction?: ModalFooterActionProps;
  tertiaryAction?: ModalFooterActionProps;
};

export const ModalFooter = ({ children, primaryAction, secondaryAction, tertiaryAction }: ModalFooterProps) => {
  // Conditional rendering: Render children if provided, otherwise render actions
  return children ? (
    <Portal hostName="modal-footer">
      <Box className="border-border-light border-t bg-background p-4 w-full">{children}</Box>
    </Portal>
  ) : (
    <Portal hostName="modal-footer">
      {/* Adopted structure and styling from DrawerFooter, adjust classes if needed */}
      <Box className="flex-row items-center justify-between border-border-lighter border-t bg-background-dark/25 px-4 py-3 w-full">
        <Box>
          {tertiaryAction && (
            <Button
              rounded="full"
              size="sm"
              variant={tertiaryAction.variant ?? "destructive-link"}
              onPress={tertiaryAction.onPress}
              disabled={tertiaryAction.disabled}
            >
              <Text>{tertiaryAction?.label}</Text>
            </Button>
          )}
        </Box>
        <Box className="flex-row gap-2 flex-1 justify-end">
          {secondaryAction && (
            <Button
              rounded="full"
              size="sm"
              variant={secondaryAction.variant ?? "outline"}
              onPress={secondaryAction.onPress}
              disabled={secondaryAction.disabled}
            >
              <Text>{secondaryAction?.label}</Text>
            </Button>
          )}
          {primaryAction && (
            <Button
              rounded="full"
              size="sm"
              variant={primaryAction.variant ?? "default"}
              onPress={primaryAction.onPress}
              disabled={primaryAction.disabled}
              isLoading={primaryAction.isLoading}
              loadingMessage={primaryAction.loadingMessage}
            >
              {primaryAction.iconBefore && (
                <Box className="mr-2">
                  {/* Assuming SVG icons might be used, adjust styling as needed */}
                  <primaryAction.iconBefore className="h-4 w-4" />
                </Box>
              )}
              <Text>{primaryAction.label}</Text>
              {primaryAction.iconAfter && (
                <Box className="ml-2">
                  {/* Assuming SVG icons might be used, adjust styling as needed */}
                  <primaryAction.iconAfter className="h-4 w-4" />
                </Box>
              )}
            </Button>
          )}
        </Box>
      </Box>
    </Portal>
  );
};

// Added default export for consistency if needed, though named export is fine
// export default ModalFooter;
