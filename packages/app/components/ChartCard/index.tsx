import ChartDownIcon from "@/assets/icons/chart-down.svg";
import ChartUpIcon from "@/assets/icons/chart-up.svg";
import { cx } from "@/utils";
import type { VariantProps } from "cva";
import { forwardRef } from "react";
import { Box, Center, Heading, Text } from "../ui";
import { chartCardVariants } from "./style";

export interface ChartCardProps {
  title: string;
  value: string;
  change: {
    type: "up" | "down";
    value: string;
    description: string;
  };
  children: React.ReactNode;
  className?: string;
}

export const ChartCard = forwardRef<
  React.ElementRef<typeof Box>,
  React.ComponentPropsWithoutRef<typeof Box> & ChartCardProps & VariantProps<typeof chartCardVariants>
>(({ className, size, title, children, value = "ND", change, ...props }, ref) => {
  const upDownColorClasses = {
    "text-success-700": change.type === "up",
    "text-destructive-700": change.type === "down",
  };

  const badgeBackgroundClasses = {
    "bg-success-50": change.type === "up",
    "bg-destructive-50": change.type === "down",
  };

  const badgeBorderClasses = {
    "border-success-100": change.type === "up",
    "border-destructive-100": change.type === "down",
  };

  return (
    <Box className={cx(chartCardVariants({ size }), className)}>
      <Box className="flex flex-row items-center justify-between border-border-light border-b px-4 py-3">
        <Heading size="sm" className="">
          {title}
        </Heading>
      </Box>
      <Box className="flex-1 flex-row rounded-xl border border-border-lightest bg-background">
        {change && (
          <Box className="z-10 flex-1 justify-start p-4">
            <Text className="mb-2 font-bold text-2xl text-text">{value}</Text>
            <Center
              className={cx(
                "w-fit flex-row justify-start rounded-xl border border-border-light px-2 py-1",
                { ...badgeBackgroundClasses },
                { ...badgeBorderClasses },
              )}
            >
              {change.type === "up" ? (
                <ChartUpIcon className={cx("h-3 w-3", { ...upDownColorClasses })} />
              ) : (
                <ChartDownIcon className={cx("h-3 w-3", { ...upDownColorClasses })} />
              )}
              <Text className={cx("font-bold text-xs", { ...upDownColorClasses })}>{change.value}</Text>
              <Text className="ml-1 text-text-secondary text-xs">{change.description}</Text>
            </Center>
          </Box>
        )}
        <Box className="flex-1 overflow-visible">{children}</Box>
      </Box>
    </Box>
  );
});

export default ChartCard;
