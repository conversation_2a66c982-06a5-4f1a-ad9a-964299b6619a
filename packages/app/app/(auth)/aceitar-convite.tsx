import * as React from "react";
import { ActivityIndicator, Platform } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { useSwitchTeamAccount } from '@/hooks/useSwitchTeamAccount';
import { But<PERSON> } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import { Box, Center, Heading } from "@/components/ui";
import { useSupabaseAuth } from "@/hooks/useSupabaseAuth";
import { useAcceptInvitation } from "@/hooks/mutations/useAcceptInvitation";
import { useLookupTeamInvite } from "@/hooks/useLookupTeamInvite";
import Reanimated, { FadeIn } from "react-native-reanimated";
import UsersIcon from "@/assets/icons/user-multiple-02.svg";
import { Link } from "expo-router";
import { EmptyPagePlaceholder } from "@/components/EmptyPagePlaceholder";
import ErrorIcon from "@/assets/icons/alert-square.svg";
import SuccessIcon from "@/assets/icons/checkmark-circle-02.svg";

export default function AcceptInviteScreen() {
  const { token, email } = useLocalSearchParams<{ token: string; email?: string }>();
  const router = useRouter();
  const { session, loading: authLoading } = useSupabaseAuth();
  const switchTeamAccount = useSwitchTeamAccount();
  const { data: invitationData, isLoading: isLoadingInvitation, error: invitationError } = useLookupTeamInvite(token);

  // State for the acceptance flow
  const [acceptanceState, setAcceptanceState] = React.useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = React.useState<string | null>(null);

  const acceptInvitationMutation = useAcceptInvitation();

  // State to track if we're redirecting to login
  const [isRedirecting, setIsRedirecting] = React.useState(false);

  React.useEffect(() => {
    // Automatically try to accept if the user is logged in and not already in the process
    if (session && token && acceptanceState === 'idle' && !authLoading) {
      handleAcceptInvitation();
    }
  }, [session, token, authLoading, acceptanceState]);

  // Handle mutation state changes
  React.useEffect(() => {
    if (acceptInvitationMutation.isSuccess) {
      setAcceptanceState('success');
      if (Platform.OS === 'web') {
        localStorage.removeItem('pendingInviteToken');
      }
    }
    if (acceptInvitationMutation.isError) {
      setAcceptanceState('error');
      setErrorMessage(acceptInvitationMutation.error?.message || 'Ocorreu um erro desconhecido.');
    }
    if (acceptInvitationMutation.isPending) {
      setAcceptanceState('loading');
    }
  }, [acceptInvitationMutation.isSuccess, acceptInvitationMutation.isError, acceptInvitationMutation.isPending, acceptInvitationMutation.error]);

  // Handle accepting invitation
  const handleAcceptInvitation = async () => {
    const tokenToUse = token || (Platform.OS === "web" ? localStorage.getItem("pendingInviteToken") : null);

    if (!tokenToUse) {
      console.log("No token available for invitation acceptance");
      setAcceptanceState('error');
      setErrorMessage("Token de convite não encontrado.");
      return;
    }

    acceptInvitationMutation.mutate({ token: tokenToUse });
  };

  // Handle sign in and then accept flow
  const handleSignInAndAccept = () => {
    setIsRedirecting(true);
    if (Platform.OS === "web") {
      localStorage.setItem("pendingInviteToken", token || "");
    }

    router.push({
      pathname: "/entrar",
      params: {
        returnTo: `/aceitar-convite?token=${token}`,
        email: invitationData?.invitation?.email || email,
      },
    });
  };

  // For debugging
  console.log("Render state:", {
    isLoadingInvitation,
    authLoading,
    hasError: !!invitationError,
    hasData: !!invitationData,
    active: invitationData?.active,
  });

  // Show loading state while checking invitation, auth, or accepting the invite
  if (isLoadingInvitation || authLoading || acceptInvitationMutation.isPending) {
    console.log("Showing loading state");
    return (
      <Center className="h-full w-full">
        <ActivityIndicator size="large" color="#0000ff" />
        <Text className="mt-4 text-text-secondary">
          {acceptInvitationMutation.isPending ? 'Aceitando convite...' : 'Verificando convite...'}
        </Text>
      </Center>
    );
  }

  // Show error if invitation is invalid or expired
  if (invitationError) {
    console.log("Showing error state:", invitationError.message);
    return (
      <Center className="h-full w-full">
        <Box className="w-full max-w-md rounded-lg border border-border bg-background p-8">
          <Heading size="lg" className="mb-4 text-foreground">Convite inválido</Heading>
          <Text className="mb-6 text-text-secondary">
            {invitationError.message}
          </Text>
          <Link href="/" asChild>
            <Button className="w-full">
              <Text>Voltar para página inicial</Text>
            </Button>
          </Link>
        </Box>
      </Center>
    );
  }

  // Ensure invitationData exists
  if (!invitationData) {
    console.log("No invitation data");
    return (
      <Center className="h-full w-full">
        <Box className="w-full max-w-md rounded-lg border border-border bg-background p-8">
          <Heading size="lg" className="mb-4 text-foreground">Convite não encontrado</Heading>
          <Text className="mb-6 text-text-secondary">
            Não foi possível verificar os detalhes do convite.
          </Text>
          <Link href="/" asChild>
            <Button className="w-full">
              <Text>Voltar para página inicial</Text>
            </Button>
          </Link>
        </Box>
      </Center>
    );
  }

  // Check if invitation is active
  if (!invitationData.active) {
    console.log("Invitation not active");
    return (
      <Center className="h-full w-full">
        <Box className="w-full max-w-md rounded-lg border border-border bg-background p-8">
          <Heading size="lg" className="mb-4 text-foreground">Convite expirado</Heading>
          <Text className="mb-6 text-text-secondary">
            Este convite está expirado ou não é válido.
          </Text>
          <Link href="/" asChild>
            <Button className="w-full">
              <Text>Voltar para página inicial</Text>
            </Button>
          </Link>
        </Box>
      </Center>
    );
  }

  // Handle success state
  if (acceptanceState === 'success') {
    return (
      <EmptyPagePlaceholder
        title="Convite aceito com sucesso!"
        description="Você foi adicionado à equipe e já pode começar a colaborar."
        illustration={SuccessIcon}
        button={{
          label: 'Ir para o painel',
          onPress: () => {
            if (invitationData?.invitation?.team_id) {
              switchTeamAccount(invitationData.invitation.team_id, () => {
                router.replace('/(app)' as any);
              });
            } else {
              router.replace('/(app)' as any);
            }
          },
          position: 'after',
          icon: 'navigate',
        }}
      />
    );
  }

  // Handle error state during acceptance
  if (acceptanceState === 'error') {
    return (
      <EmptyPagePlaceholder
        title="Erro ao aceitar convite"
        description={errorMessage || 'Ocorreu um erro desconhecido. Tente novamente.'}
        illustration={ErrorIcon}
        button={{
          label: 'Tentar novamente',
          onPress: () => setAcceptanceState('idle'),
          position: 'after',
        }}
      />
    );
  }

  // Default invitation screen
  return (
    <Center className="h-full w-full">
      <Box className="w-full max-w-md rounded-xl border border-border-light bg-background p-16">
        <Center className="mb-6">
          <Box className="mb-4 rounded-full bg-primary-50 p-4">
            <UsersIcon className="h-8 w-8 text-primary" />
          </Box>
          <Heading size="lg" className="mb-2 text-center text-foreground">
            Você foi convidado(a)
          </Heading>
          <Text className="text-center text-text-secondary">
            <Text className="font-bold">{invitationData.invitation?.team_name}</Text> convidou você para se juntar ao time
          </Text>
          {invitationData.invitation?.email && (
            <Center className="my-4 p-2 bg-background-dark rounded-lg border border-border-light w-full">
              <Text className="text-center text-text-secondary">
                {invitationData.invitation.email}
              </Text>
            </Center>
          )}
          {invitationData.invitation?.invited_by?.name && (
            <Text className="text-center text-text-secondary">
              Convidado por: {invitationData.invitation.invited_by.name}
            </Text>
          )}
        </Center>

        {session ? (
          <Button
            onPress={handleAcceptInvitation}
            className="w-full"
            disabled={acceptInvitationMutation.isPending}
          >
            {acceptInvitationMutation.isPending ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text>Aceitar convite</Text>
            )}
          </Button>
        ) : (
          <Button
            onPress={handleSignInAndAccept}
            className="w-full"
            disabled={isRedirecting}
            rounded={"full"}
          >
            {isRedirecting ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text>Entrar e aceitar convite</Text>
            )}
          </Button>
        )}

        <Text className="mt-4 text-center text-sm text-text-tertiary">
          Ao aceitar, você terá acesso ao painel administrativo desta equipe.
        </Text>
      </Box>
    </Center>
  );
}
