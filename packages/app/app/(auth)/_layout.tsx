import { Box, Center, Text } from "@/components/ui";
import { Slot } from "expo-router";
import { Image } from "react-native";
import { useState } from "react";
// import { useSharedValue } from "react-native-reanimated";
// import Carousel, {
//   type ICarouselInstance,
//   Pagination,
// } from "react-native-reanimated-carousel";
// import CircleArrowRight from "../../assets/icons/circle-arrow-right.svg";
// import CircleArrowLeft from "../../assets/icons/circle-arrow-left.svg";

const testimonials = [
  {
    name: "<PERSON>",
    image: require("../../assets/images/testimonial-monica.jpg"),
    text: "Conseguimos colocar nossos processos repetitivos no piloto automático e economizamos muito tempo!",
    job: "CEO - PlusHaus",
  },
  {
    name: "<PERSON><PERSON>",
    image: require("../../assets/images/testimonial-henrico.jpg"),

    text: "A Imoblr nos ajudou a otimizar nossas oportunidades de negócio e diminuiu nossos custos operacionais.",
    job: "Diretor regional - Imobitop",
  },
];
const width = 480;

export default function AuthLayout() {
  // const sliderRef = useRef<ICarouselInstance>(null);
  // const reanimatedProgress = useSharedValue<number>(0);
  const [slideIndex, setSlideIndex] = useState(0);

  // const isClient =
  //   (Platform.OS === "web" && typeof window !== "undefined") ||
  //   Platform.OS !== "web";

  // const onPressPagination = (index: number) => {
  //   sliderRef.current?.scrollTo({
  //     /**
  //      * Calculate the difference between the current index and the target index
  //      * to ensure that the carousel scrolls to the nearest index
  //      */
  //     count: index - reanimatedProgress.value,
  //     animated: true,
  //   });
  // };

  // const goToNextSlide = () => {
  //   sliderRef.current?.next();
  // };
  // const goToPrevSlide = () => {
  //   sliderRef.current?.prev();
  // };

  return (
    <>
      <Box className="flex-1 flex-row bg-background-dark p-2 lg:p-8 xl:p-16">
        <Box className="absolute top-0 right-0 native:hidden h-full w-full bg-[linear-gradient(to_right,rgba(var(--color-primary-600),0.2)_3px,transparent_3px),linear-gradient(to_bottom,rgba(var(--color-primary-600),0.2)_3px,transparent_3px)] bg-[size:2vh_2vh] opacity-80 [mask-image:radial-gradient(ellipse_12vw_4vh_at_104%_0%,#FFF_10%,transparent_800%)]" />
        <Box className="flex flex-1 flex-row rounded-l-xxl p-8">
          <Slot />
        </Box>
        <Box className="position-relative hidden w-[480px] flex-row overflow-hidden rounded-4xl shadow-3xl">
          {/* <Center className="absolute top-0 left-0 z-10 w-full">
            <Pagination.Basic
              progress={reanimatedProgress}
              data={testimonials}
              dotStyle={{
                backgroundColor: "rgba(255,255,255,0.2)",
                borderRadius: 50,
              }}
              activeDotStyle={{
                backgroundColor: "rgba(255,255,255,1)",
                borderRadius: 50,
              }}
              containerStyle={{ gap: 5, marginTop: 10 }}
              onPress={onPressPagination}
            />
          </Center> */}

          {/* {isClient && (
            <Carousel
              ref={sliderRef}
              autoPlay
              autoPlayInterval={10000}
              width={width}
              // @ts-ignore
              height="100%"
              data={testimonials}
              onProgressChange={reanimatedProgress}
              onScrollEnd={setSlideIndex}
              renderItem={({ index }) => (
                <Center className="h-full w-[480px] overflow-hidden bg-background-a3">
                  <Image
                    source={testimonials[index].image}
                    style={{ height: "100%", minWidth: width }}
                  />
                </Center>
              )}
            />
          )} */}
          <Center className="h-full w-[480px] overflow-hidden bg-background-a3">
            <Image source={testimonials[0].image} style={{ height: "100%", minWidth: width }} />
          </Center>

          <Box className="absolute bottom-0 left-0 z-10 h-[30%] w-full rounded-xxl bg-primary-800/80 px-12 py-10 backdrop-blur">
            <Text className="mb-2 font-medium text-2xl text-[#FFF]">{testimonials[slideIndex || 0].text}</Text>
            <Box className="flex flex-row items-center justify-between gap-2">
              <Box>
                <Text className="text-[#FFF] text-xl">{testimonials[slideIndex || 0].name}</Text>
                <Text className="text-[#FFF] text-sm">{testimonials[slideIndex || 0].job}</Text>
              </Box>
              {/* <Box className="flex flex-row items-center gap-2">
                <Pressable
                  onPress={goToPrevSlide}
                  className="transition-all duration-150 ease-in-out active:scale-95 active:opacity-80"
                >
                  <Icon
                    as={CircleArrowLeft}
                    className="h-[40px] w-[40px] text-[#FFF] text-xl"
                  />
                </Pressable>
                <Pressable
                  onPress={goToNextSlide}
                  className="transition-all duration-150 ease-in-out active:scale-95 active:opacity-80"
                >
                  <Icon
                    as={CircleArrowRight}
                    className="h-[40px] w-[40px] text-[#FFF] text-xl"
                  />
                </Pressable>
              </Box> */}
            </Box>
          </Box>
        </Box>
      </Box>
    </>
  );
}
