import "../global.css";

import { SessionLoadingGuard } from "@/components/SessionLoadingGuard";
import { DrawersProvider } from "@/context/drawer";
import { ModalProvider } from "@/context/modal";
import { SupabaseSessionProvider } from "@/context/supabase-session";
import { ThemeProvider } from "@/theme";
import { PortalHost, PortalProvider } from "@gorhom/portal";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useFonts } from "expo-font";
import { Slot } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { cssInterop } from "nativewind";
import { useEffect } from "react";
import { LogBox } from "react-native";
import Reanimated from "react-native-reanimated";
import Svg from "react-native-svg";
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import Toast from "@/components/Toast";
import { GluestackUIProvider } from "@/components/ui/gluestack-ui-provider";
// import Svg, { Circle } from "react-native-svg";

LogBox.ignoreAllLogs();

cssInterop(Svg as any, {
  className: {
    target: "style",
    nativeStyleToProp: { width: true, height: true },
  },
});

// cssInterop(Circle, {
//   className: {
//     target: "style",
//     nativeStyleToProp: { width: true, height: true },
//   },
// });

cssInterop(Reanimated.View, {
  className: {
    target: "style",
  },
});

const queryClient = new QueryClient();

export default function Layout() {
  const [loaded, error] = useFonts({
    "Inter-Regular": require("../assets/fonts/inter/Inter-Regular.ttf"),
    "Inter-Medium": require("../assets/fonts/inter/Inter-Medium.ttf"),
  });

  useEffect(() => {
    if (loaded || error) {
      SplashScreen.hideAsync();
    }
  }, [loaded, error]);

  if (!loaded && !error) {
    return null;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <SupabaseSessionProvider>
        <SessionLoadingGuard>
          <ThemeProvider colorScheme="brand">
            <GluestackUIProvider mode="system">
              <PortalProvider>
                <DrawersProvider>
                  <ModalProvider>
                    <SafeAreaProvider>
                      <GestureHandlerRootView>
                        <Slot />
                        <Toast />
                        <PortalHost name="default" />
                      </GestureHandlerRootView>
                    </SafeAreaProvider>
                  </ModalProvider>
                </DrawersProvider>
              </PortalProvider>
            </GluestackUIProvider>
          </ThemeProvider>
        </SessionLoadingGuard>
      </SupabaseSessionProvider>
    </QueryClientProvider>
  );
}
