import AddIcon from "@/assets/icons/add.svg";
import BuildingIcon from "@/assets/icons/building.svg";
import { EmptyPagePlaceholder } from "@/components/EmptyPagePlaceholder";
import PageLayout from "@/components/PageLayout";
import PropertiesList from "@/components/PropertiesList";
import { Button, Text } from "@/components/ui";
import { useDrawers } from "@/context/drawer";
import { usePropertiesList } from "@/hooks";
import { useThemeColor } from "@/theme";
import CityIllustration from "@platform/assets/illustrations/city.svg";
import { useState } from "react";
import { useWindowDimensions } from "react-native";

const _barData = [
  {
    value: 124,
    label: "Jan",
    topLabelComponent: () => <Text>0.7</Text>,
    labelComponent: () => <Text className="" />,
  },
  {
    value: 900,
    label: "Fev",
    topLabelComponent: () => <Text>0.8</Text>,
    labelComponent: () => <Text className="" />,
  },
  {
    value: 1312,
    label: "Mar",
    topLabelComponent: () => <Text>0.6</Text>,
    labelComponent: () => <Text className="" />,
  },
  {
    value: 3032,
    label: "Abr",
    topLabelComponent: () => <Text>0.4</Text>,
    labelComponent: () => <Text className="" />,
  },
  {
    value: 6050,
    label: "Mai",
    topLabelComponent: () => <Text>0.9</Text>,
    labelComponent: () => <Text className="" />,
    frontColor: "rgba(0, 0, 0, 0.5)",
  },
  {
    value: 12959,
    label: "Mai",
    topLabelComponent: () => <Text>0.9</Text>,
    labelComponent: () => <Text className="" />,
    frontColor: "rgba(0, 0, 0, 0.5)",
  },
];

export default function Home() {
  const { width } = useWindowDimensions();
  const { data: properties, isLoading, error } = usePropertiesList();
  const _chartStrokeColor = useThemeColor({ color: "primary", opacity: 0.25 });
  const _chartGradientColor = useThemeColor({ color: "primary", opacity: 0.2 });

  const { openCreatePropertyDrawer } = useDrawers();
  const [_chartWidth, _setChartWidth] = useState(0);
  const [_selectedProperties, setSelectedProperties] = useState<string[]>([]);

  const _handlePropertySelect = (propertyId: string) => {
    setSelectedProperties((prev) =>
      prev.includes(propertyId) ? prev.filter((id) => id !== propertyId) : [...prev, propertyId],
    );
  };

  return (
    <PageLayout
      raw
      pageTitle="Imóveis"
      description="Gerencie os seus imóveis, monitore estatísticas e movimentações."
      pageIcon={BuildingIcon}
      searchLabel="Procurar imovel..."
      actions={[
        <Button size="sm" key="add-property-header-action" onPress={openCreatePropertyDrawer}>
          <AddIcon className="mr-2 w-[16px] text-text-inverse" />
          <Text>Cadastrar imovel</Text>
        </Button>,
      ]}
    >
      {properties && properties.length > 0 ? (
        <PropertiesList />
      ) : (
        <EmptyPagePlaceholder
          illustration={CityIllustration}
          title="Nenhum imóvel cadastrado"
          description="Clique no botão abaixo para adicionar o seu primeiro imóvel"
          button={{
            label: "Adicionar um imóvel",
            icon: "add",
            position: "before",
            onPress: openCreatePropertyDrawer,
          }}
        />
      )}
    </PageLayout>
  );
}
