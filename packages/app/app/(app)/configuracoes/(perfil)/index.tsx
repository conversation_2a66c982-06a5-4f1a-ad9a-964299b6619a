import React from "react";
import { Box, Heading, Text, Textarea, FormField, FormInput, Form } from "@/components/ui";
import { useCurrentTeamId } from "@/hooks/useCurrentTeamId";
import { useTeamProfile } from "@/hooks/useTeamProfile";
import { useUpdateTeamProfile } from "@/hooks/mutations/useUpdateTeamProfile";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { ActivityIndicator } from "react-native";
import { SettingGroup } from "@/components/SettingGroup";
import { toast } from "@backpackapp-io/react-native-toast";

const DEBOUNCE_DELAY = 3000;

const useDebounce = (value: any, delay: number) => {
  const [debouncedValue, setDebouncedValue] = React.useState(value);

  React.useEffect(() => {
    const timer = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(timer);
  }, [JSON.stringify(value), delay]); // Use JSON.stringify to compare object values

  return debouncedValue;
};

// Form validation schema
const profileSchema = z.object({
  name: z.string().optional(),
  address: z.string().optional(),
  phone_number: z.string().optional(),
  whatsapp_number: z.string().optional(),
  instagram_url: z.string().optional(),
  facebook_url: z.string().optional(),
  youtube_url: z.string().optional(),
  about_us: z.string().optional(),
});

type ProfileFormValues = z.infer<typeof profileSchema>;

export default function TeamProfileScreen() {
  const [isSaving, setIsSaving] = React.useState(false);
  const [teamId] = useCurrentTeamId();
  const { data: profile, isLoading, error } = useTeamProfile(teamId);
  const updateTeamProfile = useUpdateTeamProfile(teamId);
  const initialProfileData = React.useRef<ProfileFormValues | null>(null);

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
  });

  const formValues = form.watch();
  const debouncedFormValues = useDebounce(formValues, DEBOUNCE_DELAY);

  React.useEffect(() => {
    if (profile) {
      const initialData = {
        name: profile.name || "",
        address: profile.address || "",
        phone_number: profile.phone_number || "",
        whatsapp_number: profile.whatsapp_number || "",
        instagram_url: profile.instagram_url || "",
        facebook_url: profile.facebook_url || "",
        youtube_url: profile.youtube_url || "",
        about_us: profile.about_us || "",
      };
      form.reset(initialData);
      initialProfileData.current = initialData;
    }
  }, [profile, form]);

  React.useEffect(() => {
    if (!initialProfileData.current || isSaving) return;

    const changedFields = Object.keys(debouncedFormValues).filter(
      (key) => debouncedFormValues[key as keyof ProfileFormValues] !== initialProfileData.current?.[key as keyof ProfileFormValues]
    );

    if (changedFields.length > 0) {
      const changedFieldNames = changedFields.map(key => {
        switch(key) {
          case "name": return "Nome da empresa";
          case "address": return "Endereço";
          case "phone_number": return "Telefone";
          case "whatsapp_number": return "WhatsApp";
          case "instagram_url": return "Instagram";
          case "facebook_url": return "Facebook";
          case "youtube_url": return "YouTube";
          case "about_us": return "Quem somos";
          default: return key;
        }
      }).join(", ");

      setIsSaving(true);
      updateTeamProfile
        .mutateAsync(debouncedFormValues)
        .then(() => {
          toast.success(`${changedFieldNames} salvo!`);
          initialProfileData.current = debouncedFormValues;
        })
        .catch(() => {
          toast.error(`Erro ao salvar ${changedFieldNames}.`);
        })
        .finally(() => {
          setIsSaving(false);
        });
    }
  }, [debouncedFormValues, updateTeamProfile]);

  if (isLoading) {
    return (
      <Box className="flex-1 bg-background p-4">
        <Text className="mb-4 font-bold text-2xl text-foreground">Carregando...</Text>
      </Box>
    );
  }

  if (error) {
    return (
      <Box className="flex-1 bg-background p-4">
        <Text className="text-destructive">Erro ao carregar perfil: {error.message}</Text>
      </Box>
    );
  }

  const renderFormField = (name: keyof ProfileFormValues, label: string, placeholder: string, Component = FormInput) => (
    <Box className="flex-1">
      <FormField
        control={form.control}
        name={name}
        render={({ field }) => (
          <Box className="flex-1">
            <Component
              label={label}
              placeholder={placeholder}
              {...field}
              value={field.value || ""}
            />
          </Box>
        )}
      />
    </Box>
  );

  return (
    <Box className="w-full">
      <Form {...form}>
        <Box>
          <SettingGroup
            title="Sobre a empresa"
            description="Configure as informações básicas do seu negócio"
          >
            <Box className="gap-4">
              {renderFormField("name", "Nome da Empresa", "Nome da sua empresa")}
              <Box className="space-y-1">
                <FormField
                  control={form.control}
                  name="about_us"
                  render={({ field }) => (
                    <Box className="space-y-1">
                      <Text className="font-medium">Quem somos</Text>
                      <Textarea
                        numberOfLines={6}
                        placeholder="Descreva sua empresa, história, valores e diferenciais..."
                        {...field}
                        value={field.value || ""}
                      />
                      <Text className="text-xs text-text-tertiary">
                        Este texto será exibido na página "Sobre Nós" do seu site institucional.
                      </Text>
                    </Box>
                  )}
                />
              </Box>
            </Box>
          </SettingGroup>

          <SettingGroup title="Endereço" description="Endereço da sua empresa">
            <Box className="gap-4">
              {renderFormField("address", "Endereço", "Endereço completo")}
            </Box>
          </SettingGroup>

          <SettingGroup title="Contato" description="Informações de contato da sua empresa">
            <Box className="gap-4 md:flex-row">
              {renderFormField("phone_number", "Telefone", "(00) 0000-0000")}
              {renderFormField("whatsapp_number", "WhatsApp", "(00) 00000-0000")}
            </Box>
          </SettingGroup>

          <SettingGroup title="Redes Sociais" description="Links para as redes sociais da sua empresa">
            <Box className="gap-4 md:flex-row">
              {renderFormField("instagram_url", "Instagram", "https://instagram.com/seuusuario")}
              {renderFormField("facebook_url", "Facebook", "https://facebook.com/suapagina")}
              {renderFormField("youtube_url", "YouTube", "https://youtube.com/seucanal")}
            </Box>
          </SettingGroup>
        </Box>
      </Form>
    </Box>
  );
}
