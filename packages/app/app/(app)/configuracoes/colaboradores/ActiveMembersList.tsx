import React from "react";
import { <PERSON><PERSON>, FlatList, Pressable } from "react-native";
import {
  Box,
  Text,
  Avatar,
  AvatarFallback,
  Badge,
} from "@/components/ui";
import { useTeamAccountMembers } from "@/hooks/useTeamAccountMembers";
import { useCurrentUserAccountRole } from "@/hooks/useCurrentUserAccountRole";
import { useRemoveTeamMember } from "@/hooks/mutations/useRemoveTeamMember";
import { useSupabaseAuth } from "@/hooks/useSupabaseAuth";
import { getInitials } from "@/utils/getInitials";

// Simple Icon component placeholder - replace with actual icon library if available
const Icon = ({ name, size = 18, color = "gray" }: { name: string; size?: number; color?: string }) => (
  <Text style={{ fontSize: size, color: color, marginRight: 5 }}>{name === "trash" ? "🗑️" : "🔗"}</Text>
);

interface ActiveMembersListProps {
  teamId: string;
}

function ActiveMembersList({ teamId }: ActiveMembersListProps) {
  const { session } = useSupabaseAuth();
  const { data: membersData, isLoading: isLoadingMembers, error: errorMembers } = useTeamAccountMembers(teamId);
  const { data: currentUserRoleData, isLoading: isLoadingRole, error: errorRole } = useCurrentUserAccountRole(teamId);
  const removeMemberMutation = useRemoveTeamMember();

  const isOwner = currentUserRoleData?.team_role === "owner";
  const currentUserId = session?.user?.id;

  const handleRemoveMember = (userId: string, userName: string | null) => {
    if (!teamId || userId === currentUserId) return; // Prevent self-removal
    Alert.alert("Remover Membro", `Tem certeza que deseja remover ${userName || "este membro"} da equipe?`, [
      { text: "Cancelar", style: "cancel" },
      {
        text: "Remover",
        style: "destructive",
        onPress: async () => {
          try {
            await removeMemberMutation.mutateAsync({ teamId, userId });
            // TODO: Show success feedback (toast)
            console.log("Member removed");
          } catch (err: unknown) {
            // TODO: Show error feedback (toast)
            console.error("Failed to remove member:", err instanceof Error ? err.message : String(err));
          }
        },
      },
    ]);
  };

  const isLoading = isLoadingMembers || isLoadingRole;
  const error = errorMembers || errorRole;

  if (isLoading) {
    return (
      <Box className="py-4">
        <Text className="text-foreground">Carregando membros...</Text>
      </Box>
    );
  }

  if (error) {
    return (
      <Box className="py-4">
        <Text className="text-destructive">Erro ao carregar membros: {error.message}</Text>
      </Box>
    );
  }

  return (
    <FlatList
      data={membersData}
      keyExtractor={(item) => item.user_id}
      renderItem={({ item }) => (
        <Box className="mb-2 flex-row items-center rounded-lg border border-border bg-card p-3">
          <Avatar alt={item.name || "Usuário sem nome"} className="mr-4 bg-background-darkest">
            <AvatarFallback>
              <Text className="font-medium">{getInitials(item.name || item.email || "Usuário")}</Text>
            </AvatarFallback>
          </Avatar>
          <Box>
            <Box className="flex-row items-center">
              <Text className="font-medium text-foreground">{item.name || "-"}</Text>
              {item.user_id === currentUserId && (
                <Badge className="ml-2" variant="outline" size="2xs">
                  <Text>você</Text>
                </Badge>
              )}
            </Box>
            <Text className="text-text-secondary text-xs">{item.email}</Text>
          </Box>
          <Badge
            className="ml-6 rounded-full bg-primary-50"
            variant={item.account_role === "owner" ? "primary-outline" : "outline"}
            size="sm"
          >
            <Text>{item.account_role === "owner" ? "Gestor" : "Colaborador"}</Text>
          </Badge>
          {isOwner &&
            item.user_id !== currentUserId &&
            item.account_role !== "owner" && ( // Prevent removing self or other owners for simplicity
              <Pressable onPress={() => handleRemoveMember(item.user_id, item.name)} hitSlop={10}>
                <Icon name="trash" color="red" />
              </Pressable>
            )}
        </Box>
      )}
    />
  );
}

export default ActiveMembersList;
export { ActiveMembersList };