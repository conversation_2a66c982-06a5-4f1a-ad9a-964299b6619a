import React, { useState } from "react";
import { FlatList, Pressable, ActivityIndicator, Alert, Platform } from "react-native";
import {
  Box,
  Text,
  Badge,
  Center,
  Heading,
  Avatar,
  AvatarFallback,
  Button,
} from "@/components/ui";
import Modal, { ModalFooter } from "@/components/Modal";
import { FeaturedIcon } from "@/components/FeaturedIcon";
import { useTeamInvitations } from "@/hooks/useTeamInvitations";
import { useDeleteInvitation } from "@/hooks/mutations/useDeleteInvitation";
import { useResendInvitation } from "@/hooks/mutations/useResendInvitation";
import { toast, Toasts, useToaster } from "@backpackapp-io/react-native-toast";
import MailIcon from '@platform/assets/icons/mail.svg';
import TrashIcon from '@platform/assets/icons/delete-02.svg';
import AlertCircleIcon from '@platform/assets/icons/alert-circle.svg';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import ExpiresInIcon from '@platform/assets/icons/clock-04.svg';

dayjs.extend(relativeTime);

interface PendingInvitesListProps {
  teamId: string;
}

function PendingInvitesList({ teamId }: PendingInvitesListProps) {
  const [invitationToDelete, setInvitationToDelete] = useState<any | null>(null);
  const deleteInvitationMutation = useDeleteInvitation();
  const resendInvitationMutation = useResendInvitation();
  const { toasts, handlers } = useToaster();

  console.log(toasts)

  const {
    data: invitationsData,
    isLoading: isLoadingInvitations,
    error: errorInvitations,
  } = useTeamInvitations(teamId);

  const handleDeleteInvitation = (invitation: any) => {
    setInvitationToDelete(invitation);
  };

  const deleteInvitation = async (invitationId: string, teamId: string) => {
    try {
      await deleteInvitationMutation.mutateAsync({ invitationId, teamId });
      setInvitationToDelete(null);
      toast.success("Convite revogado com sucesso!");
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      console.error("Failed to delete invitation:", errorMessage);
      toast.error("Falha ao revogar o convite.");
      setInvitationToDelete(null);
    }
  };

  if (isLoadingInvitations) {
    return (
      <Center className="py-4">
        <ActivityIndicator size="large" />
        <Text className="mt-2">Carregando convites...</Text>
      </Center>
    );
  }

  if (errorInvitations) {
    return (
      <Text className="text-destructive py-4">
        Erro ao carregar convites: {errorInvitations.message}
      </Text>
    );
  }

  if (!invitationsData || invitationsData.length === 0) {
    return null;
  }

  return (
    <>
      <Heading className="mt-8 mb-1">Convites pendentes</Heading>
      <Text className="text-sm text-text-tertiary mb-4">
        Convites aguardando o cadastro do colaborador
      </Text>
      <FlatList
        data={invitationsData}
        keyExtractor={(item) => item.invitation_id}
        renderItem={({ item }) => (
          <Box className="group mb-2 flex-row items-center justify-between rounded-xl bg-background-dark p-3">
            <Avatar alt={item.email} className="mr-4 bg-background border border-border-lighter">
              <AvatarFallback>
                <MailIcon className="h-4 w-4 text-text" />
              </AvatarFallback>
            </Avatar>
            <Box className="flex-1">
              <Box className="flex-row items-center">
                <Text className="font-medium text-foreground text-sm">{item.email}</Text>
                <Badge
                  className="ml-2 w-fit rounded-full"
                  variant={item.team_role === "owner" ? "primary-outline" : "outline"}
                  size="xs"
                >
                  <Text className="capitalize">{item.team_role === "owner" ? "Gestor" : "Colaborador"}</Text>
                </Badge>
              </Box>
              <Text className="mt-1 font-medium text-text-secondary text-xs">
                Convidado em {new Date(item.created_at).toLocaleDateString()} <Text className="pl-2 text-text-tertiary text-xs"><ExpiresInIcon className="h-4 w-4 mr-0.5 -mt-0.5 text-text-tertiary inline" /> Expira em {dayjs(item.expires_at).fromNow()}</Text>
              </Text>
            </Box>
            <Center className="absolute right-0 opacity-0 gap-2 group-hover:opacity-100 transition-opacity flex-row p-6">
              <Button variant="outline" size="xs" onPress={async () => {
                try {
                  await resendInvitationMutation.mutateAsync({ invitationId: item.invitation_id, teamId });
                  toast.success(`Convite reenviado para ${item.email} com sucesso!`);
                } catch (err) {
                  toast.error('Falha ao reenviar o convite.');
                }
              }}>
                <MailIcon className="h-4 w-4 mr-1.5 text-text-secondary" />
                <Text className="text-sm">Reenviar convite</Text>
              </Button>
              <Button variant="outline" size="xs" onPress={() => handleDeleteInvitation(item)}>
                <TrashIcon className="h-4 w-4 text-destructive" />
              </Button>
            </Center>
          </Box>
        )}
      />

      {/* Delete Confirmation Modal */}
      <Modal isOpen={!!invitationToDelete} onClose={() => setInvitationToDelete(null)} size="sm">
        <Center className="p-6 items-center">
          <FeaturedIcon variant="gradient" color="destructive" size="lg">
            <AlertCircleIcon className="h-5 w-5 text-[#FFF]" />
          </FeaturedIcon>
          <Heading size="lg" className="mt-4 mb-2 text-foreground">
            Tem certeza?
          </Heading>
          <Text className="text-text-secondary text-center">
            Confirme que deseja revogar o convite para
          </Text>
          <Center className="my-2 bg-background-dark rounded-lg p-1 w-full h-10 overflow-hidden border border-border-lighter">
            <Text className="font-bold text-text">{invitationToDelete?.email}</Text>
          </Center>
          <Text className="text-center text-sm text-text-tertiary pt-2">
            O convite que enviamos para este colaborador será revogado e você precisará criar um novo convite para adicionar este colaborador à sua organização.
          </Text>

          <ModalFooter
            primaryAction={{
              label: "Revogar convite",
              onPress: () => {
                if (invitationToDelete && teamId) {
                  deleteInvitation(invitationToDelete.invitation_id, teamId);
                }
              },
              variant: "destructive",
            }}
            tertiaryAction={{
              label: "Fechar",
              onPress: () => setInvitationToDelete(null),
              variant: "link",
            }}
          />
        </Center>
      </Modal>
    </>
  );
}

export default PendingInvitesList;
export { PendingInvitesList };