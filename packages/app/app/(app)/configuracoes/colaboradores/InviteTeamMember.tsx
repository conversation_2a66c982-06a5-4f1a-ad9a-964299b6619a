import React, { useState } from "react";
import {
  Box,
  Text,
  Button,
  Input,
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
  Heading,
  Center,
} from "@/components/ui";
import SentIcon from '@platform/assets/icons/solid/sent.svg'
import MailSentIllustration from '@platform/assets/illustrations/mail-sent.svg'
// import MailAccountIcon from '@platform/assets/icons/mail-account.svg'
import { useCreateInvitation } from "@/hooks/mutations/useCreateInvitation";
import Modal, { ModalFooter } from "@/components/Modal";

interface InviteTeamMemberProps {
  teamId: string;
  onInviteCreated: (token: string) => void;
}

function InviteTeamMember({ teamId, onInviteCreated }: InviteTeamMemberProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [inviteEmail, setInviteEmail] = useState("");
  const [inviteRole, setInviteRole] = useState<"member" | "owner">("member");

  const createInvitationMutation = useCreateInvitation();

  const handleInvite = async () => {
    if (!teamId || !inviteEmail.trim()) {
      alert("Por favor, insira um email válido.");
      return;
    }

    try {
      const result = await createInvitationMutation.mutateAsync({
        teamId,
        email: inviteEmail,
        role: inviteRole,
      });

      console.log("Invitation created with result:", result);

      if (result.token) {
        console.log("Setting generated token to:", result.token);
        onInviteCreated(result.token);
        setIsModalOpen(true);
      } else {
        console.error("Token missing from result!", result);
        alert("Erro ao criar convite: Token não encontrado.");
      }
    } catch (err: unknown) {
      console.error("Failed to create invite:", err instanceof Error ? err.message : String(err));
      alert("Erro ao criar convite: " + (err instanceof Error ? err.message : String(err)));
    }
  };

  const dismissSuccessModal = () => {
    setIsModalOpen(false);
    setInviteEmail("");
    setInviteRole("member");
  };

  return (
    <>
      <Box>
        <Heading>
          Enviar convite
        </Heading>
        <Text className="text-sm text-text-tertiary my-1">
          Digite o email e selecione a função do colaborador que deseja convidar
        </Text>
        <Box className="md:flex-row items-center w-full mt-4">
          <Box className="flex-row flex-1">
            <Box className="flex-1 bg-background">
              <Input
                fullWidth
                placeholder="Exemplo: <EMAIL>"
                value={inviteEmail}
                onChangeText={setInviteEmail}
                className="rounded-r-none border-r-0"
                keyboardType="email-address"
                autoCapitalize="none"
              // beforeIcon={MailAccountIcon}
              />
            </Box>
            <Select
              selectedValue={inviteRole}
              onValueChange={(value: string) => setInviteRole(value as "member" | "owner")}
            >
              <SelectTrigger className="w-32 rounded-l-none">
                <SelectInput
                  placeholder="Função"
                  value={inviteRole === "member" ? "Membro" : "Gestor"}
                />
                <SelectIcon size="xl" className="text-text-tertiary" />
              </SelectTrigger>
              <SelectPortal>
                <SelectBackdrop />
                <SelectContent>
                  <SelectDragIndicatorWrapper>
                    <SelectDragIndicator />
                  </SelectDragIndicatorWrapper>
                  <SelectItem label="Membro" value="member" />
                  <SelectItem label="Gestor" value="owner" />
                </SelectContent>
              </SelectPortal>
            </Select>
          </Box>
          <Button
            variant="secondary"
            onPress={handleInvite}
            disabled={createInvitationMutation.isPending || !inviteEmail.trim()}
            className="md:ml-4 mt-2 md:mt-0 w-full md:w-auto"
            size="md"
            isLoading={createInvitationMutation.isPending}
            loadingMessage="Enviando convite..."
          >
            <Text>Convidar</Text>
            <SentIcon className="text-text ml-2 w-4 h-4 rotate-45" />
          </Button>
        </Box>
      </Box>
      <Modal isOpen={isModalOpen} onClose={dismissSuccessModal}>
        <Center className="p-8">
          <MailSentIllustration className="h-[12rem] w-[12rem] text-primary" />
          <Heading size="xl" className="mb-4">
            Convite enviado
          </Heading>
          <Text className="text-center text-sm text-text-tertiary w-[80%]">
            Conviamos um convite para o email:
          </Text>
          <Box className="bg-background-dark p-4 my-4 border border-border-light rounded-xl w-full">
            <Text className="text-center text-sm text-text-tertiary font-medium">
              {inviteEmail || '<EMAIL>'}
            </Text>
          </Box>
          <Text className="text-center text-sm text-text-tertiary w-[80%] mb-2">
            Peça para o colaborador ficar de olho em sua caixa de entrada, o email deve chegar em alguns instantes.
          </Text>
        </Center>
        <ModalFooter>
          <Button
            rounded="full"
            onPress={dismissSuccessModal}
          >
            <Text>Fechar</Text>
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
}

export default InviteTeamMember;
export { InviteTeamMember };