import { Slot } from "expo-router";
import HorizontalTabs from "@/components/HorizontalTabs";
import PageLayout, { PageRow } from "@/components/PageLayout";
import SettingsIcon from "@/assets/icons/settings-01.svg";
import { usePathname } from "expo-router";
import { router } from "expo-router";

import GeneralSettingsIcon from "@/assets/icons/settings-02.svg";
import ActiveGeneralSettingsIcon from "@/assets/icons/solid/settings-02.svg";
import InvoiceIcon from "@/assets/icons/invoice-03.svg";
import ActiveInvoiceIcon from "@/assets/icons/solid/invoice-03.svg";
import TeamIcon from "@/assets/icons/user-multiple-02.svg";
import ActiveTeamIcon from "@/assets/icons/solid/user-multiple-02.svg";
import Profile02Icon from '@platform/assets/icons/profile-02.svg';
import ActiveProfile02Icon from '@platform/assets/icons/solid/profile-02.svg';

export default function Layout() {
  const pathname = usePathname();

  const basePath = pathname.replace("/configuracoes", "");
  const basePathMatches = (pathname: string) => pathname === basePath;

  return (
    <PageLayout
      pageTitle="Configurações da organização"
      description="Gerencie configurações da sua organização como faturamento, colaboradores, etc."
      pageIcon={SettingsIcon}
      onSearch={null}
    >
      <HorizontalTabs
        tabItems={[
          {
            id: "perfil",
            label: "Perfil público",
            onPress: () => {
              router.navigate("/configuracoes");
            },
            isActive: basePathMatches(""),
            icon: Profile02Icon,
            activeIcon: ActiveProfile02Icon,
          },
          {
            id: "faturamento",
            label: "Plano e cobrança",
            onPress: () => {
              router.navigate("/configuracoes/faturamento");
            },
            isActive: basePathMatches("/faturamento"),
            icon: InvoiceIcon,
            activeIcon: ActiveInvoiceIcon,
          },
          {
            id: "colaboradores",
            label: "Colaboradores",
            onPress: () => {
              router.navigate("/configuracoes/colaboradores");
            },
            isActive: basePathMatches("/colaboradores"),
            icon: TeamIcon,
            activeIcon: ActiveTeamIcon,
          },
        ]}
      />
      <Slot />
    </PageLayout>
  );
}
