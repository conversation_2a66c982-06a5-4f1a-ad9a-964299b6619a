import CreditCardIcon from "@/assets/icons/bulk/credit-card.svg";
import TicketIcon from "@/assets/icons/bulk/ticket-01.svg";
import RecordIcon from "@/assets/icons/solid/record.svg";
import ErrorIllustration from "@/assets/illustrations/storyset/503 Error Service Unavailable-rafiki.svg";
import { CircularProgressBar } from "@/components/CircularProgressBar";
import { EmptyPagePlaceholder } from "@/components/EmptyPagePlaceholder";
import { PageRow } from "@/components/PageLayout";
import PricingTable from "@/components/PricingTable";
import { SettingGroup } from "@/components/SettingGroup";
import { Badge, Box, Button, Center, Heading, Text } from "@/components/ui";
import { useCurrentTeamId } from "@/hooks";
import { useBillingStatus } from "@/hooks/useBillingStatus";
import { useDayjs } from "@/hooks/useDayjs";
import useTeamAccountMembers from "@/hooks/useTeamAccountMembers";
import type { StripeProduct } from "@/types";
import axios from "axios";
import { useState } from "react";
import { ActivityIndicator } from "react-native";

const dayjs = useDayjs();

interface PaymentFormData {
  quantity: number;
  isYearly: boolean;
  product: StripeProduct;
  clientSecret: string;
}

export default function BillingSettingsScreen() {
  const [teamId] = useCurrentTeamId();
  const { data: teamAccountMembers } = useTeamAccountMembers(teamId);
  const { data: billingStatus, isLoading, error } = useBillingStatus(teamId);
  const [isCustomerPortalLoading, setIsCustomerPortalLoading] = useState(false);

  const manageSubscription = async () => {
    try {
      setIsCustomerPortalLoading(true);
      const response = await axios.get(`${process.env.EXPO_PUBLIC_API_URL}/billing/portal-session`, {
        params: {
          account_id: teamId,
          return_url: window.location.href,
        },
      });

      window.location.href = response.data.url;
    } catch (error) {
      console.error("Error getting portal session:", error);
      alert("Failed to open customer portal. Please try again.");
    } finally {
      setIsCustomerPortalLoading(false);
    }
  };

  const totalTrialDays = 7;
  const isTrial = billingStatus?.status === "trialing";

  if (isLoading) {
    return (
      <PageRow>
        <Center className="my-8 flex-1 flex-row rounded-xl border border-border-light bg-background p-4">
          <ActivityIndicator />
          <Text className="ml-4 text-text-secondary">Carregando dados de faturamento...</Text>
        </Center>
      </PageRow>
    );
  }

  if (error) {
    return (
      <Box className="flex-1 bg-background p-4">
        <Text className="text-destructive">Erro ao carregar status de faturamento: {error.message}</Text>
      </Box>
    );
  }

  const remainingDays = billingStatus?.trial_end
    ? Math.ceil(dayjs(billingStatus.trial_end).diff(dayjs(), "day", true))
    : 0;

  console.log({ billingStatus });

  return billingStatus?.status === "not_setup" ? (
    <PageRow className="flex-col py-8">
      <EmptyPagePlaceholder
        illustration={ErrorIllustration}
        title="Falha ao carregar dados de faturamento"
        description="Ocorreu um erro interno ao carregar os dados de faturamento do seu negócio em nosso servidor."
      />
    </PageRow>
  ) : (
    <PageRow className="flex-col">
      {isTrial ? (
        <Box>
          <SettingGroup
            title="Período de teste"
            description="Informações sobre o seu período de teste"
          >
            <Box className="flex-row items-center">
              <CircularProgressBar progress={remainingDays / totalTrialDays} label={`${remainingDays}`} />
              <Box className="ml-4 flex-1">
                <Box className="mb-1 flex-row items-center">
                  <Heading>{remainingDays} dias restantes em seu período de teste.</Heading>
                  <Badge size="sm" variant="primary-outline" className="ml-2 uppercase">
                    <Text>{billingStatus?.plan?.name}</Text>
                  </Badge>
                </Box>
                <Text className="text-text-secondary">
                  O seu período de teste expira no dia {dayjs(billingStatus.trial_end).format("DD/MM/YYYY")}. Durante os
                  próximos {remainingDays} dias você tem acesso a todos os recursos do plano {billingStatus?.plan?.name}{" "}
                  de forma totalmente gratuita!
                </Text>
              </Box>
            </Box>
          </SettingGroup>
          <SettingGroup
            title="Escolha o seu plano"
            description="Escolha até o final do seu período de teste o plano que melhor se adapta às suas necessidades."
          >
            <PricingTable />
          </SettingGroup>
        </Box>
      ) : (
        <Box className="w-full flex-row gap-8">
          <Box className="flex-1 overflow-hidden rounded-lg border border-border-light bg-background ring-4 ring-background-darker">
            <Center className="flex-row justify-start border-border-light border-b bg-background-dark px-4 py-2.5">
              <Box className="mr-3">
                <TicketIcon className="h-5 w-5 text-text-secondary" />
              </Box>
              <Heading size="sm">{billingStatus?.plan?.name}</Heading>
              <Badge className="ml-4" variant="outline-success" rounded="full" size="2xs" beforeIcon={RecordIcon}>
                <Text>ATIVO</Text>
              </Badge>
            </Center>
            <Box className="p-4">
              <Text className="text-text-secondary">
                Sua assinatura do plano {billingStatus?.plan?.name} é válida até o dia{" "}
                {dayjs(billingStatus?.expires_at).format("DD/MM/YYYY")}.
              </Text>
            </Box>
            <Box className="w-full flex-row justify-end border-border-light border-t px-4 py-3">
              <Button
                variant="outline"
                onPress={manageSubscription}
                size="xs"
                isLoading={isCustomerPortalLoading}
                loadingMessage="Preparando portal do cliente..."
              >
                <Text>Gerenciar assinatura</Text>
              </Button>
            </Box>
          </Box>
          <Box className="flex-1 overflow-hidden rounded-lg border border-border-light bg-background ring-4 ring-background-darker">
            <Center className="flex-row justify-start border-border-light border-b bg-background-dark px-4 py-2.5">
              <Box className="mr-3">
                <CreditCardIcon className="h-5 w-5 text-text-secondary" />
              </Box>
              <Heading size="xs">Dados de pagamento</Heading>
            </Center>
            <Box className="p-4">
              <Text className="text-text-secondary">
                Clique no botão abaixo para visualizar e gerenciar seus dados de pagamento.
              </Text>
            </Box>
            <Box className="w-full flex-row justify-end border-border-light border-t px-4 py-3">
              <Button
                variant="outline"
                onPress={manageSubscription}
                size="xs"
                isLoading={isCustomerPortalLoading}
                loadingMessage="Preparando portal do cliente..."
              >
                <Text>Gerenciar formas de pagamento</Text>
              </Button>
            </Box>
          </Box>
        </Box>
      )}
    </PageRow>
  );
}
