{"expo": {"newArchEnabled": true, "name": "imoblr", "slug": "imoblr", "version": "1.0.0", "scheme": "imoblr", "web": {"bundler": "metro", "output": "static", "favicon": "./assets/favicon.png"}, "platforms": ["ios", "android", "web"], "plugins": ["expo-router", "expo-secure-store", ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your friends."}], "expo-font"], "experiments": {"typedRoutes": true, "tsconfigPaths": true}, "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "br.com.imoblr"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "br.com.imoblr"}, "extra": {"eas": {"projectId": "d781a2a1-637b-45cf-bf59-c9e0eee4c17e"}}}}