import { useQuery } from "@tanstack/react-query";
import { useAxios } from "@/hooks/useAxios";
import { createTeamInvitesApi, type LookupTeamInviteResponse } from "@/utils/teamInvitesApi";

export const INVITATION_LOOKUP_KEY = "team_invitation_lookup";

export function useLookupTeamInvite(token: string | undefined | null) {
  const { axios } = useAxios();
  const teamInvitesApi = createTeamInvitesApi(axios);

  return useQuery<LookupTeamInviteResponse, Error>({
    queryKey: [INVITATION_LOOKUP_KEY, token],
    queryFn: async () => {
      if (!token) {
        throw new Error("Token is required");
      }
      return teamInvitesApi.lookupTeamInvite(token);
    },
    enabled: !!token, // Only run the query if the token exists
  });
}