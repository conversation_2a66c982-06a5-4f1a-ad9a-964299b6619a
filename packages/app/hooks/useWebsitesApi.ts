import type { Website } from "@/types/websites";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useCallback } from "react";
import { useAxios } from "./useAxios";
import { useCurrentTeamId } from "./useCurrentTeamId";

interface CreateWebsiteRequest {
  team_account_id: string;
  title: string;
  subdomain: string;
  description?: string;
  published?: boolean;
  status?: "draft" | "published" | "archived";
  theme?: {
    primary_color: string;
    secondary_color: string;
    font_family?: string;
  };
}

interface CreateWebsiteResponse {
  success: boolean;
  website: Website;
}



interface UpdateWebsiteRequest {
  title?: string;
  subdomain?: string;
  description?: string;
  published?: boolean;
  status?: "draft" | "published" | "archived";
  logo_image_url?: string;
  hero_image_url?: string;
  institutional_image_url?: string;
  theme?: {
    primary_color?: string;
    secondary_color?: string;
    font_family?: string;
  };
}

interface UpdateWebsiteResponse {
  success: boolean;
  website: Website;
}

interface DeleteWebsiteResponse {
  success: boolean;
  message: string;
  deleted_website_id: string;
}

export const useWebsite = (websiteId: string | undefined, options: { enabled?: boolean } = {}) => {
  const { getWebsite } = useWebsitesApi();
  const { enabled = true } = options;

  return useQuery<Website>({
    queryKey: ["website", websiteId],
    queryFn: async () => {
      if (!websiteId) {
        throw new Error("Website ID is required");
      }
      return await getWebsite(websiteId);
    },
    enabled: !!websiteId && enabled,
    staleTime: 30000, // Data is fresh for 30 seconds
    gcTime: 1000 * 60 * 5, // Cache for 5 minutes
    refetchOnWindowFocus: false, // Don't refetch when window regains focus
    refetchOnMount: true, // Refetch when component mounts
  });
};

export const useWebsitesApi = () => {
  const { axios } = useAxios();
  const [currentTeamId] = useCurrentTeamId();
  const queryClient = useQueryClient();

  const createWebsiteMutation = useMutation<CreateWebsiteResponse, Error, CreateWebsiteRequest>({
    mutationFn: async (data) => {
      try {
        const response = await axios.post("/websites", data);
        return response.data;
      } catch (error: any) {
        // Extract error message from API response
        const errorMessage = error.response?.data?.error || error.message || "Failed to create website";
        throw new Error(errorMessage);
      }
    },
    onSuccess: () => {
      // Invalidate and refetch the websites list
      queryClient.invalidateQueries({ queryKey: ["websites", "team", currentTeamId] });
    },
  });

  const updateWebsiteMutation = useMutation<UpdateWebsiteResponse, Error, { websiteId: string; data: UpdateWebsiteRequest }>({
    mutationFn: async ({ websiteId, data }) => {
      try {
        const response = await axios.put(`/websites/${websiteId}`, data);
        return response.data;
      } catch (error: any) {
        const errorMessage = error.response?.data?.error || error.message || "Failed to update website";
        throw new Error(errorMessage);
      }
    },
    onSuccess: () => {
      // Invalidate and refetch the websites list
      queryClient.invalidateQueries({ queryKey: ["websites", "team", currentTeamId] });
    },
  });

  const deleteWebsiteMutation = useMutation<DeleteWebsiteResponse, Error, string>({
    mutationFn: async (websiteId) => {
      try {
        const response = await axios.delete(`/websites/${websiteId}`);
        return response.data;
      } catch (error: any) {
        const errorMessage = error.response?.data?.error || error.message || "Failed to delete website";
        throw new Error(errorMessage);
      }
    },
    onSuccess: () => {
      // Invalidate and refetch the websites list
      queryClient.invalidateQueries({ queryKey: ["websites", "team", currentTeamId] });
    },
  });

  const getWebsite = useCallback(async (websiteId: string): Promise<Website> => {
    try {
      const response = await axios.get(`/websites/${websiteId}`);
      if (response.data.success) {
        return response.data.website;
      }
      throw new Error(response.data.error || "Failed to fetch website");
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || error.message || "Failed to fetch website";
      throw new Error(errorMessage);
    }
  }, [axios]);



  const useTeamWebsites = (options: { enabled?: boolean } = {}) => {
    const { enabled = true } = options;

    return useQuery<Website[]>({
      queryKey: ["websites", "team", currentTeamId],
      queryFn: async () => {
        if (!currentTeamId) {
          throw new Error("No team selected");
        }

        try {
          const response = await axios.get(`/websites/team/${currentTeamId}`);
          if (response.data.success) {
            return response.data.websites || [];
          }
          throw new Error(response.data.error || "Failed to fetch websites");
        } catch (error: any) {
          const errorMessage = error.response?.data?.error || error.message || "Failed to fetch websites";
          throw new Error(errorMessage);
        }
      },
      enabled: !!currentTeamId && enabled,
      staleTime: 30000, // Data is fresh for 30 seconds
      gcTime: 1000 * 60 * 5, // Cache for 5 minutes
      refetchOnWindowFocus: false, // Don't refetch when window regains focus
      refetchOnMount: true, // Refetch when component mounts
    });
  };

  return {
    createWebsite: async (data: CreateWebsiteRequest) => {
      const result = await createWebsiteMutation.mutateAsync(data);
      return result.website;
    },
    updateWebsite: async (websiteId: string, data: UpdateWebsiteRequest) => {
      const result = await updateWebsiteMutation.mutateAsync({ websiteId, data });
      return result.website;
    },
    deleteWebsite: async (websiteId: string) => {
      const result = await deleteWebsiteMutation.mutateAsync(websiteId);
      return result;
    },
    getWebsite,
    useTeamWebsites,
    isCreatingWebsite: createWebsiteMutation.isPending,
    isUpdatingWebsite: updateWebsiteMutation.isPending,
    isDeletingWebsite: deleteWebsiteMutation.isPending,
    createWebsiteError: createWebsiteMutation.error,
    updateWebsiteError: updateWebsiteMutation.error,
    deleteWebsiteError: deleteWebsiteMutation.error,
  };
};
