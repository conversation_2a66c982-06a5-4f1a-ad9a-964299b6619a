import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useAxios } from "@/hooks/useAxios";
import { TEAM_MEMBERS_QUERY_KEY } from "../useTeamAccountMembers";
import { createTeamInvitesApi, type AcceptTeamInviteResponse } from "@/utils/teamInvitesApi";

interface AcceptInvitationProps {
  token: string;
}

export function useAcceptInvitation() {
  const { axios } = useAxios();
  const queryClient = useQueryClient();
  const teamInvitesApi = createTeamInvitesApi(axios);

  return useMutation<AcceptTeamInviteResponse, Error, AcceptInvitationProps>({
    mutationFn: async ({ token }: AcceptInvitationProps) => {
      try {
        const response = await teamInvitesApi.acceptTeamInvite({ token });
        return response;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : String(err);
        console.error("Error in acceptInvitation:", errorMessage);
        throw new Error(errorMessage || "Failed to accept invitation");
      }
    },
    onSuccess: () => {
      // Invalidate the team members query to refresh the list
      queryClient.invalidateQueries({ queryKey: [TEAM_MEMBERS_QUERY_KEY] });
    },
  });
}
