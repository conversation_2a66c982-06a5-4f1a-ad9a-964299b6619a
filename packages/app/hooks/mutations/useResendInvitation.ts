import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useAxios } from "../useAxios";
import { createTeamInvitesApi } from "../../utils/teamInvitesApi";

interface ResendInvitationVariables {
  invitationId: string;
  teamId: string;
}

export const useResendInvitation = () => {
  const { axios } = useAxios();
  const queryClient = useQueryClient();
  const teamInvitesApi = createTeamInvitesApi(axios);

  return useMutation<void, Error, ResendInvitationVariables>({
    mutationFn: async ({ invitationId }: ResendInvitationVariables) => {
      await teamInvitesApi.resendTeamInvite(invitationId);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ['team-invitations', variables.teamId]
      });
    },
  });
};