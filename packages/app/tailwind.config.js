const plugin = require("tailwindcss/plugin");

const { hairlineWidth } = require("nativewind/theme");
const { colors } = require("./theme/colors");

/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: "class",
  content: ["./app/**/*.{js,ts,tsx}", "./components/**/*.{js,ts,tsx}", "./node_modules/@tremor/**/*.{js,ts,jsx,tsx}"],
  presets: [require("nativewind/preset")],
  theme: {
    screens: {
      sm: "640px",
      md: "768px",
      lg: "1024px",
      xl: "1280px",
      "2xl": "1600px",
      "3xl": "1920px",
    },
    fontSize: {
      "3xs": "0.56rem",
      "2xs": "0.64rem",
      xs: "0.7rem",
      sm: "0.78rem",
      md: "0.84rem",
      base: "0.84rem",
      lg: "1rem",
      xl: "1.16rem",
      "2xl": "1.32rem",
      "3xl": "1.64rem",
      "4xl": "2rem",
      "5xl": "2.4rem",
      "6xl": "3rem",
      "7xl": "3.6rem",
      "8xl": "4.8rem",
      "9xl": "6rem",
    },
    colors: {
      ...colors,
    },
    fontFamily: {
      base: ["Inter-Regular, Helvetica, Arial, sans-serif"],
      medium: ["Inter-Medium, Helvetica, Arial, sans-serif"],
      bold: ["Inter-Medium, Helvetica, Arial, sans-serif"],
      mono: ["Inter-Regular, Helvetica, Arial, sans-serif"],
      title: ["Gelica, Helvetica, Arial, sans-serif"],
    },
    borderRadius: {
      none: "0",
      xs: "0.3rem",
      sm: "0.36rem",
      DEFAULT: "0.42rem",
      md: "0.48rem",
      lg: "0.64rem",
      xl: "0.8rem",
      "2xl": "1rem",
      "3xl": "1.4rem",
      "4xl": "1.8rem",
      full: "9999px",
    },
    extend: {
      boxShadow: {
        DEFAULT: "0 2px 4px -2px var(--color-shadow-light), 0 4px 8px -2px var(--color-shadow-lighter)",
        xs: "0 1px 2px 0 var(--color-shadow-lightest)",
        sm: "0 1px 2px 0 var(--color-shadow-lighter), 0 1px 3px 0 var(--color-shadow-lightest)",
        md: "0 2px 4px -2px var(--color-shadow-light), 0 4px 8px -2px var(--color-shadow-lighter)",
        lg: "0 4px 6px -2px var(--color-shadow), 0 12px 16px -4px var(--color-shadow-light)",
        xl: "0 8px 8px -4px var(--color-shadow-darker), 0 20px 24px -4px var(--color-shadow)",
        "2xl": "0 24px 48px -12px var(--color-shadow-darkest)",
        "3xl": "0 32px 64px -12px var(--color-shadow-darkest)",
        "primaryButtonShadow": '0px 24px 22px rgba(23, 6, 100, 0.0372022), 0px 8.5846px 8.03036px rgba(23, 6, 100, 0.0532451), 0px 4.77692px 3.89859px rgba(23, 6, 100, 0.0667549), 0px 2.63479px 1.91116px rgba(23, 6, 100, 0.0827978), 0px 1.15891px 0.755676px rgba(23, 6, 100, 0.12)',
        "smallPrimaryButtonShadow": '0px 12px 11px rgba(23, 6, 100, 0.03), 0px 4px 4px rgba(23, 6, 100, 0.04), 0px 2px 2px rgba(23, 6, 100, 0.05), 0px 1px 1px rgba(23, 6, 100, 0.06), 0px 0.5px 0.5px rgba(23, 6, 100, 0.08)',
      },
      colors: {
        ...colors,
        // -----------------------------------------------------------------------------
        // TYPOGRAPHY COLORS
        // -----------------------------------------------------------------------------
        text: {
          DEFAULT: "var(--color-text)",
          inverse: "var(--color-text-inverse)",
          "on-brand": "var(--color-text-on-brand)",
          secondary: "var(--color-text-secondary)",
          secondary_hover: "var(--color-text-secondary_hover)",
          "secondary_on-brand": "var(--color-text-secondary_on-brand)",
          tertiary: "var(--color-text-tertiary)",
          tertiary_hover: "var(--color-text-tertiary_hover)",
          "tertiary_on-brand": "var(--color-text-tertiary_on-brand)",
          quaternary: "var(--color-text-quaternary)",
          "quaternary_on-brand": "var(--color-text-quaternary_on-brand)",
          disabled: "var(--color-text-disabled)",
          placeholder: "var(--color-text-placeholder)",
          placeholder_subtle: "var(--color-text-placeholder_subtle)",
          destructive: "var(--color-text-destructive)",
          warning: "var(--color-text-warning)",
          success: "var(--color-text-success)",
        },
        shadow: {
          lightest: "var(--color-shadow-lightest)",
          lighter: "var(--color-shadow-lighter)",
          DEFAULT: "var(--color-shadow)",
          darker: "var(--color-shadow-darker)",
          darkest: "var(--color-shadow-darkest)",
        },
        border: {
          darkest: "rgba(var(--color-border-darkest), <alpha-value>)",
          darker: "rgba(var(--color-border-darker), <alpha-value>)",
          dark: "rgba(var(--color-border-dark), <alpha-value>)",
          DEFAULT: "rgba(var(--color-border), <alpha-value>)",
          light: "rgba(var(--color-border-light), <alpha-value>)",
          lighter: "rgba(var(--color-border-lighter), <alpha-value>)",
          lightest: "rgba(var(--color-border-lightest), <alpha-value>)",
        },
        ring: {
          DEFAULT: "rgba(var(--color-ring), <alpha-value>)",
        },
        background: {
          DEFAULT: "rgba(var(--color-background), <alpha-value>)",
          inverse: "rgba(var(--color-background-inverse), <alpha-value>)",
          dark: "rgba(var(--color-background-dark), <alpha-value>)",
          darker: "rgba(var(--color-background-darker), <alpha-value>)",
          darkest: "rgba(var(--color-background-darkest), <alpha-value>)",
        },
        foreground: "hsl(var(--foreground))",
        // - start - Primary colors
        primary: {
          DEFAULT: "rgba(var(--color-primary), <alpha-value>)",
        },
        "primary-5": "rgba(var(--color-primary-5), <alpha-value>)",
        "primary-25": "rgba(var(--color-primary-25), <alpha-value>)",
        "primary-50": "rgba(var(--color-primary-50), <alpha-value>)",
        "primary-100": "rgba(var(--color-primary-100), <alpha-value>)",
        "primary-200": "rgba(var(--color-primary-200), <alpha-value>)",
        "primary-300": "rgba(var(--color-primary-300), <alpha-value>)",
        "primary-400": "rgba(var(--color-primary-400), <alpha-value>)",
        "primary-500": "rgba(var(--color-primary-500), <alpha-value>)",
        "primary-600": "rgba(var(--color-primary-600), <alpha-value>)",
        "primary-700": "rgba(var(--color-primary-700), <alpha-value>)",
        "primary-800": "rgba(var(--color-primary-800), <alpha-value>)",
        "primary-900": "rgba(var(--color-primary-900), <alpha-value>)",
        "primary-950": "rgba(var(--color-primary-950), <alpha-value>)",
        // - end - Primary colors
        // - start - Secondary colors
        secondary: {
          DEFAULT: "rgba(var(--color-secondary), <alpha-value>)",
        },
        "secondary-50": "rgba(var(--color-secondary-50), <alpha-value>)",
        "secondary-100": "rgba(var(--color-secondary-100), <alpha-value>)",
        "secondary-200": "rgba(var(--color-secondary-200), <alpha-value>)",
        "secondary-300": "rgba(var(--color-secondary-300), <alpha-value>)",
        "secondary-400": "rgba(var(--color-secondary-400), <alpha-value>)",
        "secondary-500": "rgba(var(--color-secondary-500), <alpha-value>)",
        "secondary-600": "rgba(var(--color-secondary-600), <alpha-value>)",
        "secondary-700": "rgba(var(--color-secondary-700), <alpha-value>)",
        "secondary-800": "rgba(var(--color-secondary-800), <alpha-value>)",
        "secondary-900": "rgba(var(--color-secondary-900), <alpha-value>)",
        "secondary-950": "rgba(var(--color-secondary-950), <alpha-value>)",
        // - end - Secondary colors
        // - start - Tertiary colors
        tertiary: {
          DEFAULT: "rgba(var(--color-tertiary), <alpha-value>)",
        },
        "tertiary-50": "rgba(var(--color-tertiary-50), <alpha-value>)",
        "tertiary-100": "rgba(var(--color-tertiary-100), <alpha-value>)",
        "tertiary-200": "rgba(var(--color-tertiary-200), <alpha-value>)",
        "tertiary-300": "rgba(var(--color-tertiary-300), <alpha-value>)",
        "tertiary-400": "rgba(var(--color-tertiary-400), <alpha-value>)",
        "tertiary-500": "rgba(var(--color-tertiary-500), <alpha-value>)",
        "tertiary-600": "rgba(var(--color-tertiary-600), <alpha-value>)",
        "tertiary-700": "rgba(var(--color-tertiary-700), <alpha-value>)",
        "tertiary-800": "rgba(var(--color-tertiary-800), <alpha-value>)",
        "tertiary-900": "rgba(var(--color-tertiary-900), <alpha-value>)",
        "tertiary-950": "rgba(var(--color-tertiary-950), <alpha-value>)",
        // - end - Tertiary colors
        // - start - Quaternary colors
        quaternary: {
          DEFAULT: "rgba(var(--color-quaternary), <alpha-value>)",
        },
        "quaternary-50": "rgba(var(--color-quaternary-50), <alpha-value>)",
        "quaternary-100": "rgba(var(--color-quaternary-100), <alpha-value>)",
        "quaternary-200": "rgba(var(--color-quaternary-200), <alpha-value>)",
        "quaternary-300": "rgba(var(--color-quaternary-300), <alpha-value>)",
        "quaternary-400": "rgba(var(--color-quaternary-400), <alpha-value>)",
        "quaternary-500": "rgba(var(--color-quaternary-500), <alpha-value>)",
        "quaternary-600": "rgba(var(--color-quaternary-600), <alpha-value>)",
        "quaternary-700": "rgba(var(--color-quaternary-700), <alpha-value>)",
        "quaternary-800": "rgba(var(--color-quaternary-800), <alpha-value>)",
        "quaternary-900": "rgba(var(--color-quaternary-900), <alpha-value>)",
        "quaternary-950": "rgba(var(--color-quaternary-950), <alpha-value>)",
        // - end - Quaternary colors
        // - start - Gray colors
        "gray-50": "rgba(var(--color-gray-50), <alpha-value>)",
        "gray-100": "rgba(var(--color-gray-100), <alpha-value>)",
        "gray-200": "rgba(var(--color-gray-200), <alpha-value>)",
        "gray-300": "rgba(var(--color-gray-300), <alpha-value>)",
        "gray-400": "rgba(var(--color-gray-400), <alpha-value>)",
        "gray-500": "rgba(var(--color-gray-500), <alpha-value>)",
        "gray-600": "rgba(var(--color-gray-600), <alpha-value>)",
        "gray-700": "rgba(var(--color-gray-700), <alpha-value>)",
        "gray-800": "rgba(var(--color-gray-800), <alpha-value>)",
        "gray-900": "rgba(var(--color-gray-900), <alpha-value>)",
        "gray-950": "rgba(var(--color-gray-950), <alpha-value>)",
        // - end - Gray colors
        // - start - Success colors
        success: {
          DEFAULT: "rgba(var(--color-success), <alpha-value>)",
        },
        "success-50": "rgba(var(--color-success-50), <alpha-value>)",
        "success-100": "rgba(var(--color-success-100), <alpha-value>)",
        "success-200": "rgba(var(--color-success-200), <alpha-value>)",
        "success-300": "rgba(var(--color-success-300), <alpha-value>)",
        "success-400": "rgba(var(--color-success-400), <alpha-value>)",
        "success-500": "rgba(var(--color-success-500), <alpha-value>)",
        "success-600": "rgba(var(--color-success-600), <alpha-value>)",
        "success-700": "rgba(var(--color-success-700), <alpha-value>)",
        "success-800": "rgba(var(--color-success-800), <alpha-value>)",
        "success-900": "rgba(var(--color-success-900), <alpha-value>)",
        "success-950": "rgba(var(--color-success-950), <alpha-value>)",
        // - end - Success colors
        // - start - Destructive colors
        destructive: {
          DEFAULT: "rgba(var(--color-destructive), <alpha-value>)",
        },
        "destructive-50": "rgba(var(--color-destructive-50), <alpha-value>)",
        "destructive-100": "rgba(var(--color-destructive-100), <alpha-value>)",
        "destructive-200": "rgba(var(--color-destructive-200), <alpha-value>)",
        "destructive-300": "rgba(var(--color-destructive-300), <alpha-value>)",
        "destructive-400": "rgba(var(--color-destructive-400), <alpha-value>)",
        "destructive-500": "rgba(var(--color-destructive-500), <alpha-value>)",
        "destructive-600": "rgba(var(--color-destructive-600), <alpha-value>)",
        "destructive-700": "rgba(var(--color-destructive-700), <alpha-value>)",
        "destructive-800": "rgba(var(--color-destructive-800), <alpha-value>)",
        "destructive-900": "rgba(var(--color-destructive-900), <alpha-value>)",
        "destructive-950": "rgba(var(--color-destructive-950), <alpha-value>)",
        // - end - Destructive colors
        // - start - Warning colors
        warning: {
          DEFAULT: "rgba(var(--color-warning), <alpha-value>)",
        },
        "warning-5": "rgba(var(--color-warning-5), <alpha-value>)",
        "warning-25": "rgba(var(--color-warning-25), <alpha-value>)",
        "warning-50": "rgba(var(--color-warning-50), <alpha-value>)",
        "warning-100": "rgba(var(--color-warning-100), <alpha-value>)",
        "warning-200": "rgba(var(--color-warning-200), <alpha-value>)",
        "warning-300": "rgba(var(--color-warning-300), <alpha-value>)",
        "warning-400": "rgba(var(--color-warning-400), <alpha-value>)",
        "warning-500": "rgba(var(--color-warning-500), <alpha-value>)",
        "warning-600": "rgba(var(--color-warning-600), <alpha-value>)",
        "warning-700": "rgba(var(--color-warning-700), <alpha-value>)",
        "warning-800": "rgba(var(--color-warning-800), <alpha-value>)",
        "warning-900": "rgba(var(--color-warning-900), <alpha-value>)",
        "warning-950": "rgba(var(--color-warning-950), <alpha-value>)",
        // - end - Warning colors
      },
      borderWidth: {
        hairline: hairlineWidth(),
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
};
