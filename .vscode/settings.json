{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "biomejs.biome",
  "editor.codeActionsOnSave": {
    "source.organizeImports.biome": "explicit",
    "source.fixAll.biome": "explicit"
  },
  "runOnSave.commands": [
    {
      // Match any python files by language id.
      "match": "\\.(ts|tsx|js|jsx|html)$",
      // "ignore": "*.astro",
      "command": "npx biome lint \"${file}\" --write --unsafe",
      "doNotDisturb": true
    }
  ],
  "biome.projects": [
    {
      "app": "packages/app",
      "api": "packages/api",
      "customer-sites": "packages/customer-sites",
      "proxy": "packages/proxy",
      "supabase": "packages/supabase"
    }
  ],
  "editor.formatOnPaste": true,
  "typescript.tsdk": "node_modules/typescript/lib"
}